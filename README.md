# 角色自动绑定插件

专业的Maya Python插件，用于角色自动绑定，具有现代化项目结构和行业标准实践。

## 功能特性

- **自动骨骼创建**：生成完整的角色骨骼，具有规范的命名约定
- **控制系统**：自动创建动画控制器，支持自定义形状和颜色
- **蒙皮工具**：高级自动蒙皮，包含权重优化和质量检查
- **面部绑定**：全面的面部控制设置，集成BlendShape系统
- **IK/FK系统**：手臂和腿部的无缝IK/FK切换
- **用户友好界面**：分步骨骼绑定向导，提供实时反馈
- **质量保证**：内置验证和错误检查工具
- **可扩展架构**：模块化设计，便于自定义和扩展

## 项目结构

```
character_auto_rigging/
├── README.md                 # 本文件
├── setup.py                  # Python包安装配置
├── requirements.txt          # Python依赖项
├── .gitignore               # Git忽略规则
├── maya_plugin/             # 主插件包
│   ├── __init__.py          # 包初始化
│   ├── plugin.py            # 主插件入口点
│   ├── commands/            # Maya命令
│   │   ├── __init__.py
│   │   ├── auto_rig_command.py      # 主要自动绑定命令
│   │   ├── skeleton_command.py     # 骨骼创建命令
│   │   └── skinning_command.py     # 蒙皮工具命令
│   ├── nodes/               # 自定义Maya节点
│   │   ├── __init__.py
│   │   ├── ik_fk_switch_node.py    # IK/FK切换节点
│   │   └── control_shape_node.py   # 控制器形状管理节点
│   ├── rigging/             # 核心绑定模块
│   │   ├── __init__.py
│   │   ├── skeleton.py      # 骨骼创建和管理
│   │   ├── controls.py      # 控制器创建和设置
│   │   ├── skinning.py      # 蒙皮和权重工具
│   │   ├── facial.py        # 面部绑定工具
│   │   └── constraints.py   # 约束和连接工具
│   ├── ui/                  # 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py   # 主绑定界面
│   │   ├── skeleton_ui.py   # 骨骼设置界面
│   │   └── skinning_ui.py   # 蒙皮工具界面
│   └── utils/               # 实用函数
│       ├── __init__.py
│       ├── maya_utils.py    # Maya实用函数
│       ├── naming.py        # 命名规范工具
│       └── validation.py    # 质量保证工具
├── tests/                   # 单元测试
│   ├── __init__.py
│   ├── test_skeleton.py     # 骨骼系统测试
│   ├── test_controls.py     # 控制系统测试
│   └── test_skinning.py     # 蒙皮系统测试
├── docs/                    # 文档
│   ├── user_guide.md        # 用户指南
│   ├── api_reference.md     # API文档
│   └── rigging_standards.md # 绑定标准和规范
├── examples/                # 使用示例
│   ├── basic_character_rig.py      # 基础角色绑定示例
│   ├── advanced_facial_rig.py     # 高级面部绑定示例
│   └── custom_control_shapes.py   # 自定义控制器创建示例
└── presets/                 # 绑定预设和模板
    ├── biped_skeleton.json  # 双足骨骼模板
    ├── quadruped_skeleton.json # 四足骨骼模板
    └── control_shapes.json  # 控制器形状库
```

## 安装说明

### 快速开始
1. 克隆或下载此项目
2. 将 `maya_plugin` 文件夹复制到您的Maya脚本目录
3. 在Maya中，转到 窗口 > 设置/首选项 > 插件管理器
4. 浏览到 `maya_plugin/plugin.py` 并加载插件
5. 通过"角色绑定"菜单访问绑定工具

### 开发环境设置
1. 安装开发依赖项：
   ```bash
   pip install -r requirements.txt
   ```

2. 设置Maya环境变量：
   ```bash
   export MAYA_PLUG_IN_PATH=/path/to/your/maya_plugin
   export PYTHONPATH=/path/to/your/project:$PYTHONPATH
   ```

## 使用方法

### 基础角色绑定工作流程

1. **准备模型**：导入角色网格并确保其清洁
2. **打开绑定界面**：
   ```python
   from maya_plugin.ui.main_window import show_rigging_window
   show_rigging_window()
   ```
3. **创建骨骼**：使用骨骼创建工具构建骨骼结构
4. **添加控制器**：为骨骼生成动画控制器
5. **绑定蒙皮**：应用自动蒙皮和权重优化
6. **测试和完善**：使用验证工具确保绑定质量

### 命令行使用

```python
# 自动绑定完整角色
import maya.cmds as cmds
cmds.autoRigCharacter(mesh="character_mesh", rigType="biped")

# 仅创建骨骼
cmds.createCharacterSkeleton(skeletonType="biped", scale=1.0)

# 应用蒙皮
cmds.autoSkinCharacter(mesh="character_mesh", skeleton="character_skeleton")
```

## Testing

Run tests using pytest:
```bash
pytest tests/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Requirements

- Autodesk Maya 2020 or later
- Python 3.7+
