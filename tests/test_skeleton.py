"""
骨骼系统测试

此模块包含骨骼创建和管理功能的单元测试。
"""

import unittest
import maya.cmds as cmds
import maya.standalone

# 如果Maya尚未运行，尝试初始化Maya独立模式
try:
    maya.standalone.initialize()
except:
    pass  # Maya已经在运行


class TestSkeletonBuilder(unittest.TestCase):
    """骨骼构建器的测试用例。"""
    
    def setUp(self):
        """在每个测试方法之前设置测试环境。"""
        # 清空场景
        cmds.file(new=True, force=True)
    
    def tearDown(self):
        """在每个测试方法之后清理。"""
        # 清空场景
        cmds.file(new=True, force=True)
    
    def test_skeleton_builder_creation(self):
        """测试骨骼构建器创建。"""
        try:
            from maya_plugin.rigging.skeleton import SkeletonBuilder
            
            # 创建骨骼构建器
            skeleton_builder = SkeletonBuilder("test", "biped")
            self.assertIsNotNone(skeleton_builder)
            self.assertEqual(skeleton_builder.character_name, "test")
            self.assertEqual(skeleton_builder.skeleton_type, "biped")
            
        except ImportError as e:
            self.skipTest(f"骨骼模块未找到: {str(e)}")
    
    def test_biped_skeleton_creation(self):
        """测试双足骨骼创建。"""
        try:
            from maya_plugin.rigging.skeleton import SkeletonBuilder
            
            # 创建骨骼构建器
            skeleton_builder = SkeletonBuilder("test", "biped")
            
            # 创建骨骼
            joints = skeleton_builder.create_skeleton(scale=1.0)
            
            # 验证骨骼创建
            self.assertIsInstance(joints, list)
            self.assertGreater(len(joints), 0)
            
            # 检查主要关节是否存在
            root_joint = skeleton_builder.get_joint("root")
            self.assertIsNotNone(root_joint)
            self.assertTrue(cmds.objExists(root_joint))
            
        except ImportError as e:
            self.skipTest(f"骨骼模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"骨骼创建失败: {str(e)}")
    
    def test_custom_joint_addition(self):
        """测试自定义关节添加。"""
        try:
            from maya_plugin.rigging.skeleton import SkeletonBuilder
            
            # 创建骨骼构建器
            skeleton_builder = SkeletonBuilder("test", "biped")
            
            # 创建基础骨骼
            skeleton_builder.create_skeleton(scale=1.0)
            
            # 添加自定义关节
            custom_joint = skeleton_builder.add_custom_joint(
                "custom_spine", (0, 12, 0), parent="spine_01"
            )
            
            # 验证自定义关节
            self.assertIsNotNone(custom_joint)
            self.assertTrue(cmds.objExists(custom_joint))
            
        except ImportError as e:
            self.skipTest(f"骨骼模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"自定义关节添加失败: {str(e)}")
    
    def test_joint_mirroring(self):
        """测试关节镜像。"""
        try:
            from maya_plugin.rigging.skeleton import SkeletonBuilder
            
            # 创建骨骼构建器
            skeleton_builder = SkeletonBuilder("test", "biped")
            
            # 创建基础骨骼
            skeleton_builder.create_skeleton(scale=1.0)
            
            # 获取镜像前的关节数量
            joints_before = len(skeleton_builder.get_all_joints())
            
            # 执行镜像
            skeleton_builder.mirror_joints(("_L", "_R"))
            
            # 获取镜像后的关节数量
            joints_after = len(skeleton_builder.get_all_joints())
            
            # 验证镜像结果（应该有更多关节）
            self.assertGreaterEqual(joints_after, joints_before)
            
        except ImportError as e:
            self.skipTest(f"骨骼模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"关节镜像失败: {str(e)}")
    
    def test_skeleton_scaling(self):
        """测试骨骼缩放。"""
        try:
            from maya_plugin.rigging.skeleton import SkeletonBuilder
            
            # 创建骨骼构建器
            skeleton_builder = SkeletonBuilder("test", "biped")
            
            # 创建缩放的骨骼
            joints = skeleton_builder.create_skeleton(scale=2.0)
            
            # 验证骨骼创建
            self.assertIsInstance(joints, list)
            self.assertGreater(len(joints), 0)
            
            # 检查根关节位置（应该被缩放）
            root_joint = skeleton_builder.get_joint("root")
            if root_joint:
                # 根关节应该存在
                self.assertTrue(cmds.objExists(root_joint))
            
        except ImportError as e:
            self.skipTest(f"骨骼模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"骨骼缩放失败: {str(e)}")
    
    def test_quadruped_skeleton(self):
        """测试四足骨骼创建。"""
        try:
            from maya_plugin.rigging.skeleton import SkeletonBuilder
            
            # 创建四足骨骼构建器
            skeleton_builder = SkeletonBuilder("test", "quadruped")
            
            # 创建骨骼
            joints = skeleton_builder.create_skeleton(scale=1.0)
            
            # 验证骨骼创建
            self.assertIsInstance(joints, list)
            self.assertGreater(len(joints), 0)
            
            # 检查根关节
            root_joint = skeleton_builder.get_joint("root")
            self.assertIsNotNone(root_joint)
            
        except ImportError as e:
            self.skipTest(f"骨骼模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"四足骨骼创建失败: {str(e)}")


class TestNamingConvention(unittest.TestCase):
    """命名规范的测试用例。"""
    
    def setUp(self):
        """在每个测试方法之前设置测试环境。"""
        pass
    
    def tearDown(self):
        """在每个测试方法之后清理。"""
        pass
    
    def test_naming_convention_creation(self):
        """测试命名规范创建。"""
        try:
            from maya_plugin.utils.naming import NamingConvention
            
            # 创建命名规范
            naming = NamingConvention("test")
            self.assertIsNotNone(naming)
            self.assertEqual(naming.character_name, "test")
            
        except ImportError as e:
            self.skipTest(f"命名模块未找到: {str(e)}")
    
    def test_joint_naming(self):
        """测试关节命名。"""
        try:
            from maya_plugin.utils.naming import NamingConvention
            
            naming = NamingConvention("hero")
            
            # 测试基本关节命名
            joint_name = naming.get_joint_name("spine")
            self.assertEqual(joint_name, "hero_spine_jnt")
            
            # 测试带侧面的关节命名
            joint_name = naming.get_joint_name("upperarm", side="left")
            self.assertEqual(joint_name, "hero_upperarm_L_jnt")
            
            # 测试带索引的关节命名
            joint_name = naming.get_joint_name("spine", index=1)
            self.assertEqual(joint_name, "hero_spine_01_jnt")
            
        except ImportError as e:
            self.skipTest(f"命名模块未找到: {str(e)}")
    
    def test_control_naming(self):
        """测试控制器命名。"""
        try:
            from maya_plugin.utils.naming import NamingConvention
            
            naming = NamingConvention("hero")
            
            # 测试基本控制器命名
            control_name = naming.get_control_name("spine")
            self.assertEqual(control_name, "hero_spine_ctrl")
            
            # 测试带类型的控制器命名
            control_name = naming.get_control_name("upperarm", side="left", control_type="fk")
            self.assertEqual(control_name, "hero_upperarm_L_fk_ctrl")
            
        except ImportError as e:
            self.skipTest(f"命名模块未找到: {str(e)}")
    
    def test_name_validation(self):
        """测试名称验证。"""
        try:
            from maya_plugin.utils.naming import NamingConvention
            
            naming = NamingConvention("hero")
            
            # 测试有效名称
            self.assertTrue(naming.is_valid_name("hero_spine_01_L_jnt"))
            self.assertTrue(naming.is_valid_name("hero_upperarm_ctrl"))
            
            # 测试无效名称
            self.assertFalse(naming.is_valid_name("invalid name"))
            self.assertFalse(naming.is_valid_name(""))
            self.assertFalse(naming.is_valid_name("123invalid"))
            
        except ImportError as e:
            self.skipTest(f"命名模块未找到: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
