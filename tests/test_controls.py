"""
控制器系统测试

此模块包含控制器创建和管理功能的单元测试。
"""

import unittest
import maya.cmds as cmds
import maya.standalone

# 如果Maya尚未运行，尝试初始化Maya独立模式
try:
    maya.standalone.initialize()
except:
    pass  # Maya已经在运行


class TestControlBuilder(unittest.TestCase):
    """控制器构建器的测试用例。"""
    
    def setUp(self):
        """在每个测试方法之前设置测试环境。"""
        # 清空场景
        cmds.file(new=True, force=True)
    
    def tearDown(self):
        """在每个测试方法之后清理。"""
        # 清空场景
        cmds.file(new=True, force=True)
    
    def test_control_builder_creation(self):
        """测试控制器构建器创建。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            # 创建控制器构建器
            control_builder = ControlBuilder("test")
            self.assertIsNotNone(control_builder)
            self.assertEqual(control_builder.character_name, "test")
            
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
    
    def test_basic_control_creation(self):
        """测试基本控制器创建。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            # 创建控制器构建器
            control_builder = ControlBuilder("test")
            
            # 创建控制器
            control, group = control_builder.create_control(
                name="spine",
                shape="circle",
                color="blue",
                size=2.0,
                position=(0, 10, 0)
            )
            
            # 验证控制器创建
            self.assertIsNotNone(control)
            self.assertIsNotNone(group)
            self.assertTrue(cmds.objExists(control))
            self.assertTrue(cmds.objExists(group))
            
            # 检查控制器位置
            position = cmds.xform(group, query=True, worldSpace=True, translation=True)
            self.assertAlmostEqual(position[0], 0.0, places=3)
            self.assertAlmostEqual(position[1], 10.0, places=3)
            self.assertAlmostEqual(position[2], 0.0, places=3)
            
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"控制器创建失败: {str(e)}")
    
    def test_control_shapes(self):
        """测试不同控制器形状。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            control_builder = ControlBuilder("test")
            
            # 测试不同形状
            shapes = ["circle", "square", "cube", "sphere", "arrow", "cross", "diamond", "pyramid"]
            
            for i, shape in enumerate(shapes):
                control, group = control_builder.create_control(
                    name=f"test_{shape}",
                    shape=shape,
                    color="blue",
                    size=1.0,
                    position=(i * 3, 0, 0)
                )
                
                # 验证控制器创建
                self.assertIsNotNone(control)
                self.assertTrue(cmds.objExists(control))
                
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"控制器形状测试失败: {str(e)}")
    
    def test_control_colors(self):
        """测试控制器颜色。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            control_builder = ControlBuilder("test")
            
            # 测试不同颜色
            colors = ["red", "blue", "yellow", "green", "purple", "orange"]
            
            for i, color in enumerate(colors):
                control, group = control_builder.create_control(
                    name=f"test_{color}",
                    shape="circle",
                    color=color,
                    size=1.0,
                    position=(0, i * 3, 0)
                )
                
                # 验证控制器创建
                self.assertIsNotNone(control)
                self.assertTrue(cmds.objExists(control))
                
                # 检查颜色设置
                shapes = cmds.listRelatives(control, shapes=True)
                if shapes:
                    override_enabled = cmds.getAttr(f"{shapes[0]}.overrideEnabled")
                    self.assertTrue(override_enabled)
                
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"控制器颜色测试失败: {str(e)}")
    
    def test_control_for_joint(self):
        """测试为关节创建控制器。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            control_builder = ControlBuilder("test")
            
            # 创建测试关节
            test_joint = cmds.joint(name="test_spine_jnt")
            cmds.xform(test_joint, worldSpace=True, translation=(0, 10, 0))
            
            # 为关节创建控制器
            control, group = control_builder.create_control_for_joint(
                test_joint,
                shape="circle",
                color="yellow",
                size=2.0
            )
            
            # 验证控制器创建
            self.assertIsNotNone(control)
            self.assertIsNotNone(group)
            self.assertTrue(cmds.objExists(control))
            self.assertTrue(cmds.objExists(group))
            
            # 检查控制器位置是否与关节匹配
            joint_pos = cmds.xform(test_joint, query=True, worldSpace=True, translation=True)
            control_pos = cmds.xform(group, query=True, worldSpace=True, translation=True)
            
            self.assertAlmostEqual(joint_pos[0], control_pos[0], places=3)
            self.assertAlmostEqual(joint_pos[1], control_pos[1], places=3)
            self.assertAlmostEqual(joint_pos[2], control_pos[2], places=3)
            
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"关节控制器创建失败: {str(e)}")
    
    def test_control_constraint(self):
        """测试控制器约束。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            control_builder = ControlBuilder("test")
            
            # 创建测试关节
            test_joint = cmds.joint(name="test_spine_jnt")
            cmds.xform(test_joint, worldSpace=True, translation=(0, 10, 0))
            
            # 创建控制器
            control, group = control_builder.create_control(
                name="spine",
                shape="circle",
                color="blue",
                size=2.0,
                position=(0, 10, 0)
            )
            
            # 约束控制器到关节
            control_builder.constrain_control_to_joint("spine", test_joint)
            
            # 验证约束创建
            constraints = cmds.listConnections(test_joint, type="parentConstraint")
            self.assertIsNotNone(constraints)
            self.assertGreater(len(constraints), 0)
            
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"控制器约束测试失败: {str(e)}")
    
    def test_control_retrieval(self):
        """测试控制器检索。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            control_builder = ControlBuilder("test")
            
            # 创建多个控制器
            control_names = ["spine", "neck", "head"]
            
            for name in control_names:
                control_builder.create_control(
                    name=name,
                    shape="circle",
                    color="blue",
                    size=1.0,
                    position=(0, 0, 0)
                )
            
            # 测试单个控制器检索
            spine_control = control_builder.get_control("spine")
            self.assertIsNotNone(spine_control)
            self.assertTrue(cmds.objExists(spine_control))
            
            # 测试所有控制器检索
            all_controls = control_builder.get_all_controls()
            self.assertEqual(len(all_controls), len(control_names))
            
            for name in control_names:
                self.assertIn(name, all_controls)
                
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"控制器检索测试失败: {str(e)}")
    
    def test_control_group_retrieval(self):
        """测试控制器组检索。"""
        try:
            from maya_plugin.rigging.controls import ControlBuilder
            
            control_builder = ControlBuilder("test")
            
            # 创建控制器
            control, group = control_builder.create_control(
                name="spine",
                shape="circle",
                color="blue",
                size=2.0,
                position=(0, 10, 0)
            )
            
            # 测试组检索
            retrieved_group = control_builder.get_control_group("spine")
            self.assertIsNotNone(retrieved_group)
            self.assertEqual(group, retrieved_group)
            self.assertTrue(cmds.objExists(retrieved_group))
            
        except ImportError as e:
            self.skipTest(f"控制器模块未找到: {str(e)}")
        except Exception as e:
            self.skipTest(f"控制器组检索测试失败: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
