"""
角色绑定示例

此脚本演示如何使用角色自动绑定插件的核心功能。
包括骨骼创建、控制器设置、蒙皮和面部绑定等。
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om


def skeleton_creation_example():
    """演示骨骼创建功能。"""
    print("=== 骨骼创建示例 ===")
    
    try:
        from maya_plugin.rigging.skeleton import SkeletonBuilder
        
        # 创建骨骼构建器
        print("1. 创建骨骼构建器...")
        skeleton_builder = SkeletonBuilder("demo", "biped")
        
        # 创建骨骼
        print("2. 创建双足骨骼...")
        joints = skeleton_builder.create_skeleton(scale=1.0)
        print(f"   已创建 {len(joints)} 个关节")
        
        # 添加自定义关节
        print("3. 添加自定义关节...")
        custom_joint = skeleton_builder.add_custom_joint(
            "custom_spine", (0, 12.5, 0), parent="spine_02"
        )
        print(f"   已添加自定义关节: {custom_joint}")
        
        # 镜像关节
        print("4. 镜像关节...")
        skeleton_builder.mirror_joints(("_L", "_R"))
        print("   关节镜像完成")
        
        print("骨骼创建示例完成！\n")
        return skeleton_builder
        
    except Exception as e:
        print(f"骨骼创建示例错误: {str(e)}\n")
        return None


def control_creation_example(skeleton_builder):
    """演示控制器创建功能。"""
    print("=== 控制器创建示例 ===")
    
    try:
        from maya_plugin.rigging.controls import ControlBuilder
        
        # 创建控制器构建器
        print("1. 创建控制器构建器...")
        control_builder = ControlBuilder("demo")
        
        # 为主要关节创建控制器
        main_joints = ["root", "spine_01", "spine_02", "spine_03", "neck_01", "head"]
        
        print("2. 为主要关节创建控制器...")
        for joint_name in main_joints:
            joint = skeleton_builder.get_joint(joint_name)
            if joint:
                control, group = control_builder.create_control_for_joint(
                    joint, shape="circle", color="yellow", size=2.0
                )
                print(f"   已创建控制器: {control}")
        
        # 为手臂创建控制器
        print("3. 为手臂创建控制器...")
        arm_joints = ["upperarm_L", "lowerarm_L", "hand_L", "upperarm_R", "lowerarm_R", "hand_R"]
        
        for joint_name in arm_joints:
            joint = skeleton_builder.get_joint(joint_name)
            if joint:
                side = "left" if "_L" in joint_name else "right"
                color = "red" if side == "left" else "blue"
                
                control, group = control_builder.create_control_for_joint(
                    joint, shape="circle", color=color, size=1.5
                )
                print(f"   已创建控制器: {control}")
        
        # 约束控制器到关节
        print("4. 设置约束...")
        for control_name in control_builder.get_all_controls():
            control = control_builder.get_control(control_name)
            joint_name = control_name.replace("_ctrl", "")
            joint = skeleton_builder.get_joint(joint_name)
            
            if joint and control:
                control_builder.constrain_control_to_joint(control_name, joint)
                print(f"   已约束 {control} 到 {joint}")
        
        print("控制器创建示例完成！\n")
        return control_builder
        
    except Exception as e:
        print(f"控制器创建示例错误: {str(e)}\n")
        return None


def skinning_example(skeleton_builder):
    """演示蒙皮功能。"""
    print("=== 蒙皮示例 ===")
    
    try:
        from maya_plugin.rigging.skinning import SkinningTools
        
        # 创建一个简单的测试网格
        print("1. 创建测试网格...")
        test_mesh = cmds.polyCylinder(name="test_character", height=18, radius=2)[0]
        cmds.move(0, 9, 0, test_mesh)
        print(f"   已创建测试网格: {test_mesh}")
        
        # 创建蒙皮工具
        print("2. 创建蒙皮工具...")
        skinning_tools = SkinningTools("demo")
        
        # 获取主要关节进行蒙皮
        main_joints = []
        for joint_name in ["root", "spine_01", "spine_02", "spine_03"]:
            joint = skeleton_builder.get_joint(joint_name)
            if joint:
                main_joints.append(joint)
        
        # 自动蒙皮
        print("3. 应用自动蒙皮...")
        skin_cluster = skinning_tools.auto_skin_mesh(
            test_mesh, main_joints, method="closest", max_influences=4
        )
        print(f"   已创建蒙皮簇: {skin_cluster}")
        
        # 平滑权重
        print("4. 平滑蒙皮权重...")
        skinning_tools.smooth_skin_weights(test_mesh, iterations=2, strength=0.5)
        print("   权重平滑完成")
        
        # 归一化权重
        print("5. 归一化权重...")
        skinning_tools.normalize_skin_weights(test_mesh)
        print("   权重归一化完成")
        
        print("蒙皮示例完成！\n")
        return skinning_tools
        
    except Exception as e:
        print(f"蒙皮示例错误: {str(e)}\n")
        return None


def facial_rigging_example(skeleton_builder):
    """演示面部绑定功能。"""
    print("=== 面部绑定示例 ===")
    
    try:
        from maya_plugin.rigging.facial import FacialRigger
        
        # 创建面部绑定器
        print("1. 创建面部绑定器...")
        facial_rigger = FacialRigger("demo")
        
        # 获取头部关节
        head_joint = skeleton_builder.get_joint("head")
        
        # 创建面部控制器
        print("2. 创建面部控制器...")
        facial_controls = facial_rigger.create_facial_controls(
            head_joint=head_joint, scale=1.0
        )
        
        for region, controls in facial_controls.items():
            print(f"   {region}: {len(controls)} 个控制器")
        
        # 创建眼球绑定
        print("3. 创建眼球绑定...")
        # 首先创建眼球关节（如果不存在）
        if head_joint:
            head_pos = cmds.xform(head_joint, query=True, worldSpace=True, translation=True)
            
            # 创建眼球关节
            eye_L_joint = cmds.joint(name="demo_eye_L_jnt")
            cmds.xform(eye_L_joint, worldSpace=True, translation=(1, head_pos[1], head_pos[2] + 1))
            cmds.parent(eye_L_joint, head_joint)
            
            eye_R_joint = cmds.joint(name="demo_eye_R_jnt")
            cmds.xform(eye_R_joint, worldSpace=True, translation=(-1, head_pos[1], head_pos[2] + 1))
            cmds.parent(eye_R_joint, head_joint)
            
            # 创建眼球绑定
            eye_rig = facial_rigger.create_eye_rig([eye_L_joint, eye_R_joint], look_at_distance=10.0)
            print(f"   已创建眼球绑定: {len(eye_rig)} 个眼球")
        
        print("面部绑定示例完成！\n")
        return facial_rigger
        
    except Exception as e:
        print(f"面部绑定示例错误: {str(e)}\n")
        return None


def constraint_example(skeleton_builder, control_builder):
    """演示约束功能。"""
    print("=== 约束示例 ===")
    
    try:
        from maya_plugin.rigging.constraints import ConstraintTools
        
        # 创建约束工具
        print("1. 创建约束工具...")
        constraint_tools = ConstraintTools("demo")
        
        # 创建一些测试定位器
        print("2. 创建测试定位器...")
        target_loc = cmds.spaceLocator(name="target_locator")[0]
        cmds.move(5, 15, 5, target_loc)
        
        driven_loc = cmds.spaceLocator(name="driven_locator")[0]
        cmds.move(0, 15, 0, driven_loc)
        
        # 创建不同类型的约束
        print("3. 创建点约束...")
        point_constraint = constraint_tools.create_point_constraint(
            [target_loc], driven_loc, maintain_offset=False
        )
        print(f"   已创建点约束: {point_constraint}")
        
        # 创建瞄准约束
        print("4. 创建瞄准约束...")
        aim_target = cmds.spaceLocator(name="aim_target")[0]
        cmds.move(10, 15, 0, aim_target)
        
        aim_constraint = constraint_tools.create_aim_constraint(
            aim_target, driven_loc,
            aim_vector=(1, 0, 0),
            up_vector=(0, 1, 0)
        )
        print(f"   已创建瞄准约束: {aim_constraint}")
        
        # 设置约束权重
        print("5. 设置约束权重...")
        constraint_tools.set_constraint_weights(point_constraint, [0.5])
        print("   约束权重设置完成")
        
        print("约束示例完成！\n")
        return constraint_tools
        
    except Exception as e:
        print(f"约束示例错误: {str(e)}\n")
        return None


def validation_example():
    """演示验证功能。"""
    print("=== 验证示例 ===")
    
    try:
        from maya_plugin.utils.validation import RigValidator
        
        # 创建验证器
        print("1. 创建绑定验证器...")
        validator = RigValidator("demo")
        
        # 验证绑定
        print("2. 验证绑定质量...")
        results = validator.validate_rig()
        
        # 显示结果
        print("3. 验证结果:")
        for category, result in results.items():
            if isinstance(result, dict) and "score" in result:
                score = result["score"]
                issues = len(result.get("issues", []))
                warnings = len(result.get("warnings", []))
                print(f"   {category}: {score}/100 (问题: {issues}, 警告: {warnings})")
        
        overall_score = results.get("overall_score", 0)
        print(f"\n   整体评分: {overall_score:.1f}/100")
        
        # 获取详细报告
        print("4. 生成详细报告...")
        report = validator.get_validation_report()
        print("   报告已生成（可通过 validator.get_validation_report() 查看）")
        
        print("验证示例完成！\n")
        return validator
        
    except Exception as e:
        print(f"验证示例错误: {str(e)}\n")
        return None


def complete_rigging_workflow():
    """完整的绑定工作流程示例。"""
    print("角色自动绑定插件 - 完整工作流程示例")
    print("=" * 60)
    
    # 清空场景
    cmds.file(new=True, force=True)
    
    # 1. 创建骨骼
    skeleton_builder = skeleton_creation_example()
    if not skeleton_builder:
        print("骨骼创建失败，停止工作流程")
        return
    
    # 2. 创建控制器
    control_builder = control_creation_example(skeleton_builder)
    if not control_builder:
        print("控制器创建失败，继续其他步骤")
    
    # 3. 应用蒙皮
    skinning_tools = skinning_example(skeleton_builder)
    if not skinning_tools:
        print("蒙皮应用失败，继续其他步骤")
    
    # 4. 面部绑定
    facial_rigger = facial_rigging_example(skeleton_builder)
    if not facial_rigger:
        print("面部绑定失败，继续其他步骤")
    
    # 5. 约束设置
    constraint_tools = constraint_example(skeleton_builder, control_builder)
    if not constraint_tools:
        print("约束设置失败，继续其他步骤")
    
    # 6. 验证绑定
    validator = validation_example()
    
    print("=" * 60)
    print("完整绑定工作流程完成！")
    print("您现在可以在Maya中查看创建的绑定。")
    print("使用验证器检查绑定质量，并根据需要进行调整。")
    
    return {
        "skeleton_builder": skeleton_builder,
        "control_builder": control_builder,
        "skinning_tools": skinning_tools,
        "facial_rigger": facial_rigger,
        "constraint_tools": constraint_tools,
        "validator": validator
    }


# 主执行
if __name__ == "__main__":
    # 检查是否在Maya中运行
    try:
        import maya.cmds as cmds
        complete_rigging_workflow()
    except ImportError:
        print("此脚本必须在Maya内部运行！")
    except Exception as e:
        print(f"运行绑定示例时出错: {str(e)}")
        print("请确保在运行此脚本之前已加载角色自动绑定插件。")
