"""
Basic Usage Examples for Character Auto Rigging Plugin

This script demonstrates how to use the various features of the character rigging plugin.
Run this script in Maya's Script Editor after loading the plugin.
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om


def auto_rig_usage():
    """Demonstrate usage of the auto rig command."""
    print("=== Auto Rig Command Usage ===")

    try:
        # Basic auto rig
        print("1. Creating default biped rig...")
        result1 = cmds.autoRigCharacter(name="hero", type="biped", scale=1.0, verbose=True)
        print(f"   Created: {result1}")

        # Skeleton only
        print("2. Creating skeleton only...")
        result2 = cmds.autoRigCharacter(name="test", skeletonOnly=True, verbose=True)
        print(f"   Created: {result2}")

        # Custom scaled rig
        print("3. Creating scaled rig...")
        result3 = cmds.autoRigCharacter(name="giant", scale=2.0, verbose=True)
        print(f"   Created: {result3}")

        print("Auto rig command usage completed successfully!\n")

    except Exception as e:
        print(f"Error in auto rig command usage: {str(e)}\n")


def example_node_usage():
    """Demonstrate usage of the example node."""
    print("=== Example Node Usage ===")
    
    try:
        # Create the node
        print("1. Creating example node...")
        node = cmds.createNode("exampleNode", name="myExampleNode")
        print(f"   Created node: {node}")
        
        # Test basic computation
        print("2. Testing basic computation (3 * 4)...")
        cmds.setAttr(f"{node}.inputA", 3.0)
        cmds.setAttr(f"{node}.inputB", 4.0)
        result = cmds.getAttr(f"{node}.output")
        print(f"   Result: {result}")
        
        # Test with different values
        print("3. Testing with different values (7.5 * 2.2)...")
        cmds.setAttr(f"{node}.inputA", 7.5)
        cmds.setAttr(f"{node}.inputB", 2.2)
        result = cmds.getAttr(f"{node}.output")
        print(f"   Result: {result}")
        
        # Test with negative values
        print("4. Testing with negative values (-5 * 3)...")
        cmds.setAttr(f"{node}.inputA", -5.0)
        cmds.setAttr(f"{node}.inputB", 3.0)
        result = cmds.getAttr(f"{node}.output")
        print(f"   Result: {result}")
        
        print("Example node usage completed successfully!\n")
        
    except Exception as e:
        print(f"Error in example node usage: {str(e)}\n")


def utility_functions_usage():
    """Demonstrate usage of utility functions."""
    print("=== Utility Functions Usage ===")
    
    try:
        from maya_plugin.utils import maya_utils
        
        # Get Maya version
        print("1. Getting Maya version...")
        version = maya_utils.get_maya_version()
        print(f"   Maya version: {version}")
        
        # Create a locator
        print("2. Creating locator at position (2, 3, 4)...")
        locator = maya_utils.create_locator_at_position((2, 3, 4), "exampleLocator")
        print(f"   Created locator: {locator}")
        
        # Get object position
        print("3. Getting locator position...")
        position = maya_utils.get_object_world_position(locator)
        print(f"   Position: {position}")
        
        # Create a cube and get its bounding box
        print("4. Creating cube and getting bounding box...")
        cube = cmds.polyCube(name="utilsCube")[0]
        bbox = maya_utils.get_object_bounding_box(cube)
        print(f"   Bounding box: {bbox}")
        
        # Test selection functions
        print("5. Testing selection functions...")
        cmds.select(cube, locator)
        selection = maya_utils.get_selected_objects()
        print(f"   Selected objects: {selection}")
        
        # Create a group
        print("6. Creating group with selected objects...")
        group = maya_utils.create_group(selection, "exampleGroup")
        print(f"   Created group: {group}")
        
        # Duplicate an object
        print("7. Duplicating cube...")
        duplicate = maya_utils.duplicate_object(cube, "duplicatedCube")
        print(f"   Duplicated object: {duplicate}")
        
        print("Utility functions usage completed successfully!\n")
        
    except Exception as e:
        print(f"Error in utility functions usage: {str(e)}\n")


def ui_usage_example():
    """Demonstrate how to open the plugin UI."""
    print("=== UI Usage Example ===")
    
    try:
        from maya_plugin.ui.main_window import show_main_window
        
        print("Opening main plugin window...")
        window = show_main_window()
        print("Main window opened successfully!")
        print("You can now interact with the plugin through the UI.\n")
        
        return window
        
    except Exception as e:
        print(f"Error opening UI: {str(e)}\n")
        return None


def node_network_example():
    """Create a more complex example using multiple nodes."""
    print("=== Node Network Example ===")
    
    try:
        # Create multiple nodes
        print("1. Creating multiple example nodes...")
        node1 = cmds.createNode("exampleNode", name="multiplier1")
        node2 = cmds.createNode("exampleNode", name="multiplier2")
        node3 = cmds.createNode("exampleNode", name="finalMultiplier")
        
        # Set up a calculation: (2 * 3) * (4 * 5) = 6 * 20 = 120
        print("2. Setting up node network: (2*3) * (4*5)...")
        
        # First node: 2 * 3 = 6
        cmds.setAttr(f"{node1}.inputA", 2.0)
        cmds.setAttr(f"{node1}.inputB", 3.0)
        
        # Second node: 4 * 5 = 20
        cmds.setAttr(f"{node2}.inputA", 4.0)
        cmds.setAttr(f"{node2}.inputB", 5.0)
        
        # Connect outputs to third node inputs
        cmds.connectAttr(f"{node1}.output", f"{node3}.inputA")
        cmds.connectAttr(f"{node2}.output", f"{node3}.inputB")
        
        # Get the final result
        print("3. Getting final result...")
        result1 = cmds.getAttr(f"{node1}.output")
        result2 = cmds.getAttr(f"{node2}.output")
        final_result = cmds.getAttr(f"{node3}.output")
        
        print(f"   Node 1 result: {result1}")
        print(f"   Node 2 result: {result2}")
        print(f"   Final result: {final_result}")
        
        print("Node network example completed successfully!\n")
        
    except Exception as e:
        print(f"Error in node network example: {str(e)}\n")


def cleanup_example():
    """Clean up objects created by examples."""
    print("=== Cleanup ===")
    
    try:
        from maya_plugin.utils import maya_utils
        
        # List of objects to clean up
        cleanup_objects = [
            "exampleCube*",
            "customCube*", 
            "verboseCube*",
            "myExampleNode*",
            "exampleLocator*",
            "utilsCube*",
            "exampleGroup*",
            "duplicatedCube*",
            "multiplier*",
            "finalMultiplier*"
        ]
        
        print("Cleaning up example objects...")
        for pattern in cleanup_objects:
            objects = cmds.ls(pattern)
            if objects:
                maya_utils.safe_delete_objects(objects)
                print(f"   Deleted: {objects}")
        
        print("Cleanup completed!\n")
        
    except Exception as e:
        print(f"Error during cleanup: {str(e)}\n")


def run_all_examples():
    """Run all examples in sequence."""
    print("Maya Plugin Project - Basic Usage Examples")
    print("=" * 50)
    
    # Clear the scene first
    cmds.file(new=True, force=True)
    
    # Run examples
    example_command_usage()
    example_node_usage()
    utility_functions_usage()
    node_network_example()
    
    # Open UI (optional)
    print("Opening plugin UI...")
    ui_window = ui_usage_example()
    
    # Ask user if they want to clean up
    print("Examples completed!")
    print("You can now explore the created objects in Maya.")
    print("Run cleanup_example() to remove all created objects.")
    
    return ui_window


# Main execution
if __name__ == "__main__":
    # Check if we're running in Maya
    try:
        import maya.cmds as cmds
        run_all_examples()
    except ImportError:
        print("This script must be run inside Maya!")
    except Exception as e:
        print(f"Error running examples: {str(e)}")
        print("Make sure the Maya plugin is loaded before running this script.")
