"""
角色自动绑定插件基础使用示例

此脚本演示如何使用角色绑定插件的各种功能。
在加载插件后，在Maya的脚本编辑器中运行此脚本。
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om


def auto_rig_usage():
    """演示自动绑定命令的使用。"""
    print("=== 自动绑定命令使用示例 ===")

    try:
        # 基础自动绑定
        print("1. 创建默认双足绑定...")
        result1 = cmds.autoRigCharacter(name="hero", type="biped", scale=1.0, verbose=True)
        print(f"   已创建: {result1}")

        # 仅骨骼
        print("2. 仅创建骨骼...")
        result2 = cmds.autoRigCharacter(name="test", skeletonOnly=True, verbose=True)
        print(f"   已创建: {result2}")

        # 自定义缩放绑定
        print("3. 创建缩放绑定...")
        result3 = cmds.autoRigCharacter(name="giant", scale=2.0, verbose=True)
        print(f"   已创建: {result3}")

        print("自动绑定命令使用完成！\n")

    except Exception as e:
        print(f"自动绑定命令使用错误: {str(e)}\n")


def example_node_usage():
    """演示示例节点的使用。"""
    print("=== 示例节点使用 ===")

    try:
        # 创建节点
        print("1. 创建示例节点...")
        node = cmds.createNode("exampleNode", name="myExampleNode")
        print(f"   已创建节点: {node}")

        # 测试基本计算
        print("2. 测试基本计算 (3 * 4)...")
        cmds.setAttr(f"{node}.inputA", 3.0)
        cmds.setAttr(f"{node}.inputB", 4.0)
        result = cmds.getAttr(f"{node}.output")
        print(f"   结果: {result}")

        # 测试不同数值
        print("3. 测试不同数值 (7.5 * 2.2)...")
        cmds.setAttr(f"{node}.inputA", 7.5)
        cmds.setAttr(f"{node}.inputB", 2.2)
        result = cmds.getAttr(f"{node}.output")
        print(f"   结果: {result}")

        # 测试负数值
        print("4. 测试负数值 (-5 * 3)...")
        cmds.setAttr(f"{node}.inputA", -5.0)
        cmds.setAttr(f"{node}.inputB", 3.0)
        result = cmds.getAttr(f"{node}.output")
        print(f"   结果: {result}")

        print("示例节点使用完成！\n")

    except Exception as e:
        print(f"示例节点使用错误: {str(e)}\n")


def utility_functions_usage():
    """演示实用函数的使用。"""
    print("=== 实用函数使用 ===")

    try:
        from maya_plugin.utils import maya_utils

        # 获取Maya版本
        print("1. 获取Maya版本...")
        version = maya_utils.get_maya_version()
        print(f"   Maya版本: {version}")

        # 创建定位器
        print("2. 在位置(2, 3, 4)创建定位器...")
        locator = maya_utils.create_locator_at_position((2, 3, 4), "exampleLocator")
        print(f"   已创建定位器: {locator}")

        # 获取对象位置
        print("3. 获取定位器位置...")
        position = maya_utils.get_object_world_position(locator)
        print(f"   位置: {position}")

        # 创建立方体并获取边界框
        print("4. 创建立方体并获取边界框...")
        cube = cmds.polyCube(name="utilsCube")[0]
        bbox = maya_utils.get_object_bounding_box(cube)
        print(f"   边界框: {bbox}")

        # 测试选择函数
        print("5. 测试选择函数...")
        cmds.select(cube, locator)
        selection = maya_utils.get_selected_objects()
        print(f"   选中对象: {selection}")

        # 创建组
        print("6. 用选中对象创建组...")
        group = maya_utils.create_group(selection, "exampleGroup")
        print(f"   已创建组: {group}")

        # 复制对象
        print("7. 复制立方体...")
        duplicate = maya_utils.duplicate_object(cube, "duplicatedCube")
        print(f"   已复制对象: {duplicate}")

        print("实用函数使用完成！\n")

    except Exception as e:
        print(f"实用函数使用错误: {str(e)}\n")


def ui_usage_example():
    """演示如何打开插件UI。"""
    print("=== UI使用示例 ===")

    try:
        from maya_plugin.ui.main_window import show_rigging_window

        print("打开主插件窗口...")
        window = show_rigging_window()
        print("主窗口打开成功！")
        print("您现在可以通过UI与插件交互。\n")

        return window

    except Exception as e:
        print(f"打开UI错误: {str(e)}\n")
        return None


def node_network_example():
    """创建使用多个节点的复杂示例。"""
    print("=== 节点网络示例 ===")

    try:
        # 创建多个节点
        print("1. 创建多个示例节点...")
        node1 = cmds.createNode("exampleNode", name="multiplier1")
        node2 = cmds.createNode("exampleNode", name="multiplier2")
        node3 = cmds.createNode("exampleNode", name="finalMultiplier")

        # 设置计算: (2 * 3) * (4 * 5) = 6 * 20 = 120
        print("2. 设置节点网络: (2*3) * (4*5)...")

        # 第一个节点: 2 * 3 = 6
        cmds.setAttr(f"{node1}.inputA", 2.0)
        cmds.setAttr(f"{node1}.inputB", 3.0)

        # 第二个节点: 4 * 5 = 20
        cmds.setAttr(f"{node2}.inputA", 4.0)
        cmds.setAttr(f"{node2}.inputB", 5.0)

        # 连接输出到第三个节点的输入
        cmds.connectAttr(f"{node1}.output", f"{node3}.inputA")
        cmds.connectAttr(f"{node2}.output", f"{node3}.inputB")

        # 获取最终结果
        print("3. 获取最终结果...")
        result1 = cmds.getAttr(f"{node1}.output")
        result2 = cmds.getAttr(f"{node2}.output")
        final_result = cmds.getAttr(f"{node3}.output")

        print(f"   节点1结果: {result1}")
        print(f"   节点2结果: {result2}")
        print(f"   最终结果: {final_result}")

        print("节点网络示例完成！\n")

    except Exception as e:
        print(f"节点网络示例错误: {str(e)}\n")


def cleanup_example():
    """清理示例创建的对象。"""
    print("=== 清理 ===")

    try:
        from maya_plugin.utils import maya_utils

        # 要清理的对象列表
        cleanup_objects = [
            "hero*",
            "test*",
            "giant*",
            "myExampleNode*",
            "exampleLocator*",
            "utilsCube*",
            "exampleGroup*",
            "duplicatedCube*",
            "multiplier*",
            "finalMultiplier*"
        ]

        print("清理示例对象...")
        for pattern in cleanup_objects:
            objects = cmds.ls(pattern)
            if objects:
                maya_utils.safe_delete_objects(objects)
                print(f"   已删除: {objects}")

        print("清理完成！\n")

    except Exception as e:
        print(f"清理过程中出错: {str(e)}\n")


def run_all_examples():
    """按顺序运行所有示例。"""
    print("角色自动绑定插件 - 基础使用示例")
    print("=" * 50)

    # 首先清空场景
    cmds.file(new=True, force=True)

    # 运行示例
    auto_rig_usage()
    example_node_usage()
    utility_functions_usage()
    node_network_example()

    # 打开UI（可选）
    print("打开插件UI...")
    ui_window = ui_usage_example()

    # 询问用户是否要清理
    print("示例完成！")
    print("您现在可以在Maya中探索创建的对象。")
    print("运行 cleanup_example() 来移除所有创建的对象。")

    return ui_window


# 主执行
if __name__ == "__main__":
    # 检查是否在Maya中运行
    try:
        import maya.cmds as cmds
        run_all_examples()
    except ImportError:
        print("此脚本必须在Maya内部运行！")
    except Exception as e:
        print(f"运行示例时出错: {str(e)}")
        print("请确保在运行此脚本之前已加载Maya插件。")
