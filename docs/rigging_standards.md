# Character Rigging Standards and Conventions

## Overview

This document outlines the standards and conventions used in the Character Auto Rigging Plugin to ensure consistency, maintainability, and industry compatibility.

## Naming Conventions

### General Pattern
All objects follow the pattern: `[character]_[component]_[index]_[side]_[type]`

### Examples
- `hero_spine_01_jnt` - First spine joint for character "hero"
- `hero_upperarm_L_ctrl` - Left upper arm control for character "hero"
- `hero_leg_R_ik_hdl` - Right leg IK handle for character "hero"

### Suffixes
- **jnt** - Joints
- **ctrl** - Controls
- **grp** - Groups
- **loc** - Locators
- **con** - Constraints
- **hdl** - Handles
- **ik** - IK handles
- **sc** - Skin clusters
- **bs** - BlendShapes

### Side Indicators
- **L** - Left side
- **R** - Right side
- **C** - Center/Middle

## Skeleton Structure

### Biped Hierarchy
```
root
└── pelvis
    ├── spine_01
    │   ├── spine_02
    │   │   └── spine_03
    │   │       ├── neck_01
    │   │       │   └── head
    │   │       ├── clavicle_L
    │   │       │   └── upperarm_L
    │   │       │       └── lowerarm_L
    │   │       │           └── hand_L
    │   │       │               ├── thumb_01_L
    │   │       │               ├── index_01_L
    │   │       │               ├── middle_01_L
    │   │       │               ├── ring_01_L
    │   │       │               └── pinky_01_L
    │   │       └── clavicle_R
    │   │           └── upperarm_R
    │   │               └── lowerarm_R
    │   │                   └── hand_R
    │   │                       ├── thumb_01_R
    │   │                       ├── index_01_R
    │   │                       ├── middle_01_R
    │   │                       ├── ring_01_R
    │   │                       └── pinky_01_R
    ├── upperleg_L
    │   └── lowerleg_L
    │       └── foot_L
    │           └── toe_L
    └── upperleg_R
        └── lowerleg_R
            └── foot_R
                └── toe_R
```

### Joint Orientation
- **Primary Axis**: X-axis points down the bone chain
- **Secondary Axis**: Y-axis points up (world Y when possible)
- **Tertiary Axis**: Z-axis follows right-hand rule
- **End Joints**: Match parent orientation

## Control Standards

### Control Shapes
- **Root/Main**: Square or diamond
- **Spine/Torso**: Circles
- **Arms**: Circles (red for left, blue for right)
- **Legs**: Circles (red for left, blue for right)
- **Hands/Feet**: Cubes
- **Fingers**: Small circles
- **Face**: Small circles or custom shapes

### Control Colors
- **Left Side**: Red (Maya color index 13)
- **Right Side**: Blue (Maya color index 6)
- **Center/Spine**: Yellow (Maya color index 17)
- **Head/Neck**: Green (Maya color index 14)
- **Special**: Purple (Maya color index 9)

### Control Hierarchy
Each control should have:
1. **Control Transform**: The actual control curve
2. **Offset Group**: Parent group for positioning
3. **SDK Group**: Optional group for set-driven keys
4. **Constraint Group**: Optional group for constraints

## Constraint Guidelines

### Parent Constraints
- Use for full transform control
- Maintain offset when possible
- Weight sum should equal 1.0

### Point Constraints
- Use for position-only control
- Common for pole vectors and aim targets

### Orient Constraints
- Use for rotation-only control
- Common for look-at systems

### Aim Constraints
- Use for automatic orientation
- Define proper up vectors
- Consider world up objects

## Skinning Standards

### Influence Limits
- **Maximum Influences**: 4 per vertex
- **Minimum Weight**: 0.01 (prune below this)
- **Weight Distribution**: Normalize to 1.0

### Weight Quality
- Smooth weight transitions
- No extreme weight values (0 or 1) except at joints
- Proper falloff from joint centers
- No unused influences

## Facial Rigging

### Control Placement
- **Eyebrows**: Above eye area, 3 controls per side
- **Eyelids**: Upper and lower, 1 control per side
- **Eyes**: Center of eye, with look-at targets
- **Cheeks**: Side of face, 1 control per side
- **Mouth**: Corners, upper, lower, and lip controls
- **Jaw**: Single control below mouth

### BlendShape Naming
- Use descriptive names: `smile`, `frown`, `blink_L`, `blink_R`
- Group by region: `mouth_`, `eye_`, `brow_`
- Use consistent prefixes for variations

## File Organization

### Group Structure
```
character_rig_grp
├── character_skeleton_grp
│   └── [all joints]
├── character_controls_grp
│   └── [all control groups]
├── character_geometry_grp
│   └── [character meshes]
└── character_extras_grp
    └── [locators, helpers, etc.]
```

### Layer Organization
- **Skeleton Layer**: All joints
- **Controls Layer**: All controls
- **Geometry Layer**: Character meshes
- **Extras Layer**: Helper objects

## Quality Assurance

### Validation Checks
1. **Naming**: All objects follow naming convention
2. **Hierarchy**: Proper group structure
3. **Transforms**: No extreme values or frozen transforms
4. **Constraints**: All constraints properly connected
5. **Skinning**: Proper weight distribution
6. **Controls**: Proper color coding and shapes

### Performance Guidelines
- Keep polygon count reasonable for real-time use
- Minimize constraint count where possible
- Use efficient deformer order
- Avoid unnecessary nodes in dependency graph

## Best Practices

### General
- Always work in centimeters
- Use consistent scale (1 unit = 1 cm)
- Keep rig at world origin
- Use proper reference coordinate systems

### Joints
- Orient joints before skinning
- Freeze transforms on controls
- Lock unused attributes
- Use consistent joint radius

### Controls
- Make controls easily selectable
- Use appropriate control sizes
- Group controls logically
- Add custom attributes where needed

### Constraints
- Use maintain offset when appropriate
- Name constraints descriptively
- Avoid constraint cycles
- Use proper constraint order

## Troubleshooting

### Common Issues
1. **Gimbal Lock**: Use proper joint orientation
2. **Flipping**: Check pole vector positions
3. **Weight Issues**: Validate skin cluster setup
4. **Performance**: Optimize constraint usage

### Debugging Tools
- Use rig validation system
- Check dependency graph
- Verify constraint weights
- Test extreme poses

## Version Control

### File Naming
- Use version numbers: `character_rig_v001.ma`
- Include date stamps for major revisions
- Use descriptive commit messages

### Backup Strategy
- Save incremental versions
- Keep working files separate from published rigs
- Document major changes

This document serves as the foundation for consistent, high-quality character rigs using the Character Auto Rigging Plugin.
