# 角色绑定标准和规范

## 概述

本文档概述了角色自动绑定插件中使用的标准和规范，以确保一致性、可维护性和行业兼容性。

## 命名规范

### 通用模式
所有对象遵循模式：`[角色名]_[组件]_[索引]_[侧面]_[类型]`

### 示例
- `hero_spine_01_jnt` - 角色"hero"的第一个脊椎关节
- `hero_upperarm_L_ctrl` - 角色"hero"的左上臂控制器
- `hero_leg_R_ik_hdl` - 角色"hero"的右腿IK手柄

### 后缀
- **jnt** - 关节
- **ctrl** - 控制器
- **grp** - 组
- **loc** - 定位器
- **con** - 约束
- **hdl** - 手柄
- **ik** - IK手柄
- **sc** - 蒙皮簇
- **bs** - 混合变形

### 侧面指示器
- **L** - 左侧
- **R** - 右侧
- **C** - 中心/中间

## 骨骼结构

### 双足层次结构
```
root（根）
└── pelvis（骨盆）
    ├── spine_01（脊椎01）
    │   ├── spine_02（脊椎02）
    │   │   └── spine_03（脊椎03）
    │   │       ├── neck_01（颈部01）
    │   │       │   └── head（头部）
    │   │       ├── clavicle_L（左锁骨）
    │   │       │   └── upperarm_L（左上臂）
    │   │       │       └── lowerarm_L（左前臂）
    │   │       │           └── hand_L（左手）
    │   │       │               ├── thumb_01_L（左拇指01）
    │   │       │               ├── index_01_L（左食指01）
    │   │       │               ├── middle_01_L（左中指01）
    │   │       │               ├── ring_01_L（左无名指01）
    │   │       │               └── pinky_01_L（左小指01）
    │   │       └── clavicle_R（右锁骨）
    │   │           └── upperarm_R（右上臂）
    │   │               └── lowerarm_R（右前臂）
    │   │                   └── hand_R（右手）
    │   │                       ├── thumb_01_R（右拇指01）
    │   │                       ├── index_01_R（右食指01）
    │   │                       ├── middle_01_R（右中指01）
    │   │                       ├── ring_01_R（右无名指01）
    │   │                       └── pinky_01_R（右小指01）
    ├── upperleg_L（左大腿）
    │   └── lowerleg_L（左小腿）
    │       └── foot_L（左脚）
    │           └── toe_L（左脚趾）
    └── upperleg_R（右大腿）
        └── lowerleg_R（右小腿）
            └── foot_R（右脚）
                └── toe_R（右脚趾）
```

### 关节方向
- **主轴**：X轴指向骨骼链下方
- **次轴**：Y轴向上（尽可能使用世界Y轴）
- **第三轴**：Z轴遵循右手法则
- **末端关节**：匹配父级方向

## 控制器标准

### 控制器形状
- **根/主要**：方形或菱形
- **脊椎/躯干**：圆形
- **手臂**：圆形（左侧红色，右侧蓝色）
- **腿部**：圆形（左侧红色，右侧蓝色）
- **手/脚**：立方体
- **手指**：小圆形
- **面部**：小圆形或自定义形状

### 控制器颜色
- **左侧**：红色（Maya颜色索引13）
- **右侧**：蓝色（Maya颜色索引6）
- **中心/脊椎**：黄色（Maya颜色索引17）
- **头部/颈部**：绿色（Maya颜色索引14）
- **特殊**：紫色（Maya颜色索引9）

### 控制器层次结构
每个控制器应该包含：
1. **控制器变换**：实际的控制器曲线
2. **偏移组**：用于定位的父组
3. **SDK组**：用于设置驱动关键帧的可选组
4. **约束组**：用于约束的可选组

## 约束指南

### 父约束
- 用于完整的变换控制
- 尽可能保持偏移
- 权重总和应等于1.0

### 点约束
- 用于仅位置控制
- 常用于极向量和瞄准目标

### 方向约束
- 用于仅旋转控制
- 常用于视线跟随系统

### 瞄准约束
- 用于自动方向控制
- 定义正确的上向量
- 考虑世界上向量对象

## 蒙皮标准

### 影响限制
- **最大影响数**：每个顶点4个
- **最小权重**：0.01（低于此值将被修剪）
- **权重分布**：归一化为1.0

### 权重质量
- 平滑的权重过渡
- 除关节处外，无极端权重值（0或1）
- 从关节中心适当衰减
- 无未使用的影响

## 面部绑定

### 控制器放置
- **眉毛**：眼部上方，每侧3个控制器
- **眼睑**：上下眼睑，每侧1个控制器
- **眼球**：眼球中心，带有视线跟随目标
- **脸颊**：面部侧面，每侧1个控制器
- **嘴部**：嘴角、上唇、下唇和唇部控制器
- **下颌**：嘴部下方单个控制器

### BlendShape命名
- 使用描述性名称：`smile`、`frown`、`blink_L`、`blink_R`
- 按区域分组：`mouth_`、`eye_`、`brow_`
- 对变化使用一致的前缀

## 文件组织

### 组结构
```
character_rig_grp（角色绑定组）
├── character_skeleton_grp（角色骨骼组）
│   └── [所有关节]
├── character_controls_grp（角色控制器组）
│   └── [所有控制器组]
├── character_geometry_grp（角色几何体组）
│   └── [角色网格]
└── character_extras_grp（角色额外组）
    └── [定位器、辅助对象等]
```

### 层组织
- **骨骼层**：所有关节
- **控制器层**：所有控制器
- **几何体层**：角色网格
- **额外层**：辅助对象

## 质量保证

### 验证检查
1. **命名**：所有对象遵循命名规范
2. **层次结构**：正确的组结构
3. **变换**：无极端值或冻结变换
4. **约束**：所有约束正确连接
5. **蒙皮**：正确的权重分布
6. **控制器**：正确的颜色编码和形状

### 性能指南
- 保持合理的多边形数量以供实时使用
- 尽可能减少约束数量
- 使用高效的变形器顺序
- 避免依赖图中的不必要节点

## 最佳实践

### 通用
- 始终使用厘米单位工作
- 使用一致的比例（1单位 = 1厘米）
- 将绑定保持在世界原点
- 使用正确的参考坐标系

### 关节
- 蒙皮前调整关节方向
- 冻结控制器上的变换
- 锁定未使用的属性
- 使用一致的关节半径

### 控制器
- 使控制器易于选择
- 使用适当的控制器尺寸
- 逻辑性地分组控制器
- 在需要时添加自定义属性

### 约束
- 适当时使用保持偏移
- 描述性地命名约束
- 避免约束循环
- 使用正确的约束顺序

## 故障排除

### 常见问题
1. **万向节锁定**：使用正确的关节方向
2. **翻转问题**：检查极向量位置
3. **权重问题**：验证蒙皮簇设置
4. **性能问题**：优化约束使用

### 调试工具
- 使用绑定验证系统
- 检查依赖图
- 验证约束权重
- 测试极端姿势

## 版本控制

### 文件命名
- 使用版本号：`character_rig_v001.ma`
- 为主要修订包含日期戳
- 使用描述性的提交消息

### 备份策略
- 保存增量版本
- 将工作文件与发布的绑定分开保存
- 记录主要更改

## 总结

本文档作为使用角色自动绑定插件创建一致、高质量角色绑定的基础。遵循这些标准和规范将确保：

- **一致性**：所有绑定遵循相同的命名和结构规范
- **可维护性**：清晰的组织和文档使绑定易于修改和更新
- **兼容性**：符合行业标准，便于团队协作
- **质量**：内置的验证和最佳实践确保绑定质量
- **效率**：标准化流程提高制作效率

通过严格遵循这些指南，您可以创建专业级别的角色绑定，满足游戏、动画和影视制作的需求。
