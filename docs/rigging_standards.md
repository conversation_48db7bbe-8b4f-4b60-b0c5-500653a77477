# 角色绑定标准和规范

## 概述

本文档概述了角色自动绑定插件中使用的标准和规范，以确保一致性、可维护性和行业兼容性。

## 命名规范

### 通用模式
所有对象遵循模式：`[角色名]_[组件]_[索引]_[侧面]_[类型]`

### 示例
- `hero_spine_01_jnt` - 角色"hero"的第一个脊椎关节
- `hero_upperarm_L_ctrl` - 角色"hero"的左上臂控制器
- `hero_leg_R_ik_hdl` - 角色"hero"的右腿IK手柄

### 后缀
- **jnt** - 关节
- **ctrl** - 控制器
- **grp** - 组
- **loc** - 定位器
- **con** - 约束
- **hdl** - 手柄
- **ik** - IK手柄
- **sc** - 蒙皮簇
- **bs** - 混合变形

### 侧面指示器
- **L** - 左侧
- **R** - 右侧
- **C** - 中心/中间

## 骨骼结构

### 双足层次结构
```
root（根）
└── pelvis（骨盆）
    ├── spine_01（脊椎01）
    │   ├── spine_02（脊椎02）
    │   │   └── spine_03（脊椎03）
    │   │       ├── neck_01（颈部01）
    │   │       │   └── head（头部）
    │   │       ├── clavicle_L（左锁骨）
    │   │       │   └── upperarm_L（左上臂）
    │   │       │       └── lowerarm_L（左前臂）
    │   │       │           └── hand_L（左手）
    │   │       │               ├── thumb_01_L（左拇指01）
    │   │       │               ├── index_01_L（左食指01）
    │   │       │               ├── middle_01_L（左中指01）
    │   │       │               ├── ring_01_L（左无名指01）
    │   │       │               └── pinky_01_L（左小指01）
    │   │       └── clavicle_R（右锁骨）
    │   │           └── upperarm_R（右上臂）
    │   │               └── lowerarm_R（右前臂）
    │   │                   └── hand_R（右手）
    │   │                       ├── thumb_01_R（右拇指01）
    │   │                       ├── index_01_R（右食指01）
    │   │                       ├── middle_01_R（右中指01）
    │   │                       ├── ring_01_R（右无名指01）
    │   │                       └── pinky_01_R（右小指01）
    ├── upperleg_L（左大腿）
    │   └── lowerleg_L（左小腿）
    │       └── foot_L（左脚）
    │           └── toe_L（左脚趾）
    └── upperleg_R（右大腿）
        └── lowerleg_R（右小腿）
            └── foot_R（右脚）
                └── toe_R（右脚趾）
```

### 关节方向
- **主轴**：X轴指向骨骼链下方
- **次轴**：Y轴向上（尽可能使用世界Y轴）
- **第三轴**：Z轴遵循右手法则
- **末端关节**：匹配父级方向

## 控制器标准

### 控制器形状
- **根/主要**：方形或菱形
- **脊椎/躯干**：圆形
- **手臂**：圆形（左侧红色，右侧蓝色）
- **腿部**：圆形（左侧红色，右侧蓝色）
- **手/脚**：立方体
- **手指**：小圆形
- **面部**：小圆形或自定义形状

### 控制器颜色
- **左侧**：红色（Maya颜色索引13）
- **右侧**：蓝色（Maya颜色索引6）
- **中心/脊椎**：黄色（Maya颜色索引17）
- **头部/颈部**：绿色（Maya颜色索引14）
- **特殊**：紫色（Maya颜色索引9）

### 控制器层次结构
每个控制器应该包含：
1. **控制器变换**：实际的控制器曲线
2. **偏移组**：用于定位的父组
3. **SDK组**：用于设置驱动关键帧的可选组
4. **约束组**：用于约束的可选组

## Constraint Guidelines

### Parent Constraints
- Use for full transform control
- Maintain offset when possible
- Weight sum should equal 1.0

### Point Constraints
- Use for position-only control
- Common for pole vectors and aim targets

### Orient Constraints
- Use for rotation-only control
- Common for look-at systems

### Aim Constraints
- Use for automatic orientation
- Define proper up vectors
- Consider world up objects

## Skinning Standards

### Influence Limits
- **Maximum Influences**: 4 per vertex
- **Minimum Weight**: 0.01 (prune below this)
- **Weight Distribution**: Normalize to 1.0

### Weight Quality
- Smooth weight transitions
- No extreme weight values (0 or 1) except at joints
- Proper falloff from joint centers
- No unused influences

## Facial Rigging

### Control Placement
- **Eyebrows**: Above eye area, 3 controls per side
- **Eyelids**: Upper and lower, 1 control per side
- **Eyes**: Center of eye, with look-at targets
- **Cheeks**: Side of face, 1 control per side
- **Mouth**: Corners, upper, lower, and lip controls
- **Jaw**: Single control below mouth

### BlendShape Naming
- Use descriptive names: `smile`, `frown`, `blink_L`, `blink_R`
- Group by region: `mouth_`, `eye_`, `brow_`
- Use consistent prefixes for variations

## File Organization

### Group Structure
```
character_rig_grp
├── character_skeleton_grp
│   └── [all joints]
├── character_controls_grp
│   └── [all control groups]
├── character_geometry_grp
│   └── [character meshes]
└── character_extras_grp
    └── [locators, helpers, etc.]
```

### Layer Organization
- **Skeleton Layer**: All joints
- **Controls Layer**: All controls
- **Geometry Layer**: Character meshes
- **Extras Layer**: Helper objects

## Quality Assurance

### Validation Checks
1. **Naming**: All objects follow naming convention
2. **Hierarchy**: Proper group structure
3. **Transforms**: No extreme values or frozen transforms
4. **Constraints**: All constraints properly connected
5. **Skinning**: Proper weight distribution
6. **Controls**: Proper color coding and shapes

### Performance Guidelines
- Keep polygon count reasonable for real-time use
- Minimize constraint count where possible
- Use efficient deformer order
- Avoid unnecessary nodes in dependency graph

## Best Practices

### General
- Always work in centimeters
- Use consistent scale (1 unit = 1 cm)
- Keep rig at world origin
- Use proper reference coordinate systems

### Joints
- Orient joints before skinning
- Freeze transforms on controls
- Lock unused attributes
- Use consistent joint radius

### Controls
- Make controls easily selectable
- Use appropriate control sizes
- Group controls logically
- Add custom attributes where needed

### Constraints
- Use maintain offset when appropriate
- Name constraints descriptively
- Avoid constraint cycles
- Use proper constraint order

## Troubleshooting

### Common Issues
1. **Gimbal Lock**: Use proper joint orientation
2. **Flipping**: Check pole vector positions
3. **Weight Issues**: Validate skin cluster setup
4. **Performance**: Optimize constraint usage

### Debugging Tools
- Use rig validation system
- Check dependency graph
- Verify constraint weights
- Test extreme poses

## Version Control

### File Naming
- Use version numbers: `character_rig_v001.ma`
- Include date stamps for major revisions
- Use descriptive commit messages

### Backup Strategy
- Save incremental versions
- Keep working files separate from published rigs
- Document major changes

This document serves as the foundation for consistent, high-quality character rigs using the Character Auto Rigging Plugin.
