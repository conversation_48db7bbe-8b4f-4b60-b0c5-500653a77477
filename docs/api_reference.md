# API参考文档

## 概述

本文档提供了角色自动绑定插件的详细API参考，包括所有模块、类和函数的说明。

## 核心模块

### maya_plugin.rigging.skeleton

#### SkeletonBuilder类

用于创建和管理角色骨骼的主要类。

```python
from maya_plugin.rigging.skeleton import SkeletonBuilder

# 初始化
skeleton_builder = SkeletonBuilder(character_name="hero", skeleton_type="biped")

# 创建骨骼
joints = skeleton_builder.create_skeleton(scale=1.0)

# 添加自定义关节
custom_joint = skeleton_builder.add_custom_joint("custom_joint", (0, 5, 0), parent="spine_01")

# 镜像关节
skeleton_builder.mirror_joints(("_L", "_R"))
```

**主要方法：**

- `create_skeleton(scale=1.0)` - 创建完整骨骼结构
- `add_custom_joint(name, position, parent=None)` - 添加自定义关节
- `mirror_joints(search_replace=("_L", "_R"))` - 镜像关节
- `get_joint(joint_name)` - 获取关节引用
- `get_all_joints()` - 获取所有关节

### maya_plugin.rigging.controls

#### ControlBuilder类

用于创建和管理动画控制器的主要类。

```python
from maya_plugin.rigging.controls import ControlBuilder

# 初始化
control_builder = ControlBuilder(character_name="hero")

# 创建控制器
control, group = control_builder.create_control(
    name="spine_ctrl",
    shape="circle",
    color="blue",
    size=2.0,
    position=(0, 10, 0)
)

# 为关节创建控制器
control, group = control_builder.create_control_for_joint(
    joint="hero_spine_01_jnt",
    shape="circle",
    color="yellow",
    size=1.5
)

# 约束控制器到关节
control_builder.constrain_control_to_joint("spine_ctrl", "hero_spine_01_jnt")
```

**主要方法：**

- `create_control(name, shape, color, size, position, rotation)` - 创建控制器
- `create_control_for_joint(joint, shape, color, size, offset)` - 为关节创建控制器
- `constrain_control_to_joint(control_name, joint, constraint_type)` - 约束控制器到关节
- `get_control(name)` - 获取控制器引用
- `get_all_controls()` - 获取所有控制器

**可用形状：**
- `circle` - 圆形
- `square` - 方形
- `cube` - 立方体
- `sphere` - 球体
- `arrow` - 箭头
- `cross` - 十字
- `diamond` - 菱形
- `pyramid` - 金字塔

**可用颜色：**
- `red` - 红色（左侧）
- `blue` - 蓝色（右侧）
- `yellow` - 黄色（中心）
- `green` - 绿色（头部/颈部）
- `purple` - 紫色（特殊）
- `orange` - 橙色
- `pink` - 粉色
- `cyan` - 青色
- `white` - 白色
- `black` - 黑色

### maya_plugin.rigging.skinning

#### SkinningTools类

用于角色蒙皮和权重管理的主要类。

```python
from maya_plugin.rigging.skinning import SkinningTools

# 初始化
skinning_tools = SkinningTools(character_name="hero")

# 自动蒙皮网格
skin_cluster = skinning_tools.auto_skin_mesh(
    mesh="character_mesh",
    joints=["hero_spine_01_jnt", "hero_upperarm_L_jnt"],
    method="closest",
    max_influences=4
)

# 复制蒙皮权重
skinning_tools.copy_skin_weights("source_mesh", "target_mesh")

# 平滑蒙皮权重
skinning_tools.smooth_skin_weights("character_mesh", iterations=3, strength=0.5)

# 归一化权重
skinning_tools.normalize_skin_weights("character_mesh")

# 修剪权重
skinning_tools.prune_skin_weights("character_mesh", threshold=0.01)
```

**主要方法：**

- `auto_skin_mesh(mesh, joints, method, max_influences)` - 自动蒙皮网格
- `copy_skin_weights(source_mesh, target_mesh, surface_association)` - 复制蒙皮权重
- `smooth_skin_weights(mesh, iterations, strength)` - 平滑蒙皮权重
- `normalize_skin_weights(mesh)` - 归一化权重
- `prune_skin_weights(mesh, threshold)` - 修剪小权重
- `get_skin_weights(mesh, vertex_index)` - 获取顶点权重
- `set_skin_weights(mesh, vertex_index, weight_dict)` - 设置顶点权重

### maya_plugin.rigging.facial

#### FacialRigger类

用于面部绑定和表情设置的主要类。

```python
from maya_plugin.rigging.facial import FacialRigger

# 初始化
facial_rigger = FacialRigger(character_name="hero")

# 创建面部控制器
controls = facial_rigger.create_facial_controls(
    head_joint="hero_head_jnt",
    scale=1.0
)

# 创建BlendShape设置
blendshape = facial_rigger.create_blendshape_setup(
    base_mesh="head_mesh",
    target_meshes=["smile_mesh", "frown_mesh", "blink_mesh"]
)

# 连接控制器到BlendShape
control_mapping = {
    "mouth_corner_L": "smile",
    "mouth_corner_R": "smile",
    "eyelid_upper_L": "blink_L"
}
facial_rigger.connect_controls_to_blendshape(blendshape, control_mapping)

# 创建眼球绑定
eye_rig = facial_rigger.create_eye_rig(
    eye_joints=["hero_eye_L_jnt", "hero_eye_R_jnt"],
    look_at_distance=10.0
)
```

**主要方法：**

- `create_facial_controls(head_joint, scale)` - 创建面部控制器
- `create_blendshape_setup(base_mesh, target_meshes)` - 创建BlendShape设置
- `connect_controls_to_blendshape(blendshape, control_mapping)` - 连接控制器到BlendShape
- `create_eye_rig(eye_joints, look_at_distance)` - 创建眼球绑定
- `get_facial_control(control_name)` - 获取面部控制器
- `get_all_facial_controls()` - 获取所有面部控制器

### maya_plugin.rigging.constraints

#### ConstraintTools类

用于创建和管理约束的主要类。

```python
from maya_plugin.rigging.constraints import ConstraintTools

# 初始化
constraint_tools = ConstraintTools(character_name="hero")

# 创建父约束
parent_constraint = constraint_tools.create_parent_constraint(
    drivers=["hero_spine_ctrl"],
    driven="hero_spine_01_jnt",
    maintain_offset=True
)

# 创建点约束
point_constraint = constraint_tools.create_point_constraint(
    drivers=["hero_hand_L_ctrl"],
    driven="hero_hand_L_jnt"
)

# 创建方向约束
orient_constraint = constraint_tools.create_orient_constraint(
    drivers=["hero_head_ctrl"],
    driven="hero_head_jnt"
)

# 创建瞄准约束
aim_constraint = constraint_tools.create_aim_constraint(
    target="target_locator",
    driven="hero_eye_L_jnt",
    aim_vector=(0, 0, 1),
    up_vector=(0, 1, 0)
)

# 设置约束权重
constraint_tools.set_constraint_weights(parent_constraint, [1.0])
```

**主要方法：**

- `create_parent_constraint(drivers, driven, maintain_offset, name)` - 创建父约束
- `create_point_constraint(drivers, driven, maintain_offset, name)` - 创建点约束
- `create_orient_constraint(drivers, driven, maintain_offset, name)` - 创建方向约束
- `create_scale_constraint(drivers, driven, maintain_offset, name)` - 创建缩放约束
- `create_aim_constraint(target, driven, aim_vector, up_vector, ...)` - 创建瞄准约束
- `create_pole_vector_constraint(pole_vector, ik_handle, name)` - 创建极向量约束
- `set_constraint_weights(constraint, weights)` - 设置约束权重
- `remove_constraint(constraint)` - 移除约束

## 实用工具模块

### maya_plugin.utils.naming

#### NamingConvention类

标准化命名规范工具。

```python
from maya_plugin.utils.naming import NamingConvention

# 初始化
naming = NamingConvention(character_name="hero")

# 获取关节名称
joint_name = naming.get_joint_name("spine", side="left", index=1)
# 结果: "hero_spine_01_L_jnt"

# 获取控制器名称
control_name = naming.get_control_name("upperarm", side="left", control_type="fk")
# 结果: "hero_upperarm_L_fk_ctrl"

# 获取组名称
group_name = naming.get_group_name("spine", group_type="offset")
# 结果: "hero_spine_offset_grp"

# 解析名称
parsed = naming.parse_name("hero_spine_01_L_jnt")
# 结果: {"character": "hero", "component": "spine", "index": 1, "side": "L", "type": "joint"}

# 验证名称
is_valid = naming.is_valid_name("hero_spine_01_L_jnt")
# 结果: True
```

### maya_plugin.utils.validation

#### RigValidator类

绑定质量验证工具。

```python
from maya_plugin.utils.validation import RigValidator

# 初始化
validator = RigValidator(character_name="hero")

# 验证绑定
results = validator.validate_rig(rig_root="hero_rig_grp")

# 获取验证报告
report = validator.get_validation_report()
print(report)

# 检查整体评分
overall_score = results["overall_score"]
print(f"绑定质量评分: {overall_score}/100")
```

**验证类别：**
- `naming` - 命名规范验证
- `hierarchy` - 层次结构验证
- `joints` - 关节设置验证
- `controls` - 控制器验证
- `constraints` - 约束验证
- `skinning` - 蒙皮验证
- `transforms` - 变换验证

## 主要命令

### autoRigCharacter

主要的自动绑定命令。

```python
import maya.cmds as cmds

# 基本用法
cmds.autoRigCharacter()

# 完整参数
cmds.autoRigCharacter(
    mesh="character_mesh",          # 角色网格（可选）
    name="hero",                    # 角色名称
    type="biped",                   # 绑定类型（biped, quadruped）
    scale=1.0,                      # 缩放比例
    verbose=True,                   # 详细输出
    skeletonOnly=False,             # 仅创建骨骼
    controlsOnly=False              # 仅创建控制器
)
```

**参数说明：**
- `mesh` - 要绑定的角色网格（可选）
- `name` - 角色名称前缀
- `type` - 绑定类型（"biped"双足, "quadruped"四足）
- `scale` - 绑定缩放比例
- `verbose` - 启用详细输出
- `skeletonOnly` - 仅创建骨骼结构
- `controlsOnly` - 仅创建控制器（需要现有骨骼）

## 错误处理

所有API函数都包含适当的错误处理：

```python
try:
    result = cmds.autoRigCharacter(name="hero", type="biped")
    print(f"绑定创建成功: {result}")
except Exception as e:
    print(f"绑定创建失败: {str(e)}")
```

## 最佳实践

1. **始终使用try-except块**处理可能的错误
2. **使用标准命名规范**确保一致性
3. **在绑定前验证输入**（网格存在性、命名等）
4. **使用验证工具**检查绑定质量
5. **保持模块化**，分步骤创建绑定组件

## 示例工作流程

```python
# 完整的角色绑定工作流程示例
import maya.cmds as cmds
from maya_plugin.rigging.skeleton import SkeletonBuilder
from maya_plugin.rigging.controls import ControlBuilder
from maya_plugin.rigging.skinning import SkinningTools
from maya_plugin.utils.validation import RigValidator

# 1. 创建骨骼
skeleton_builder = SkeletonBuilder("hero", "biped")
joints = skeleton_builder.create_skeleton(scale=1.0)

# 2. 创建控制器
control_builder = ControlBuilder("hero")
for joint_name in ["spine_01", "upperarm_L", "upperarm_R"]:
    joint = skeleton_builder.get_joint(joint_name)
    if joint:
        control_builder.create_control_for_joint(joint, "circle", "blue", 1.5)

# 3. 应用蒙皮（如果有网格）
if cmds.objExists("character_mesh"):
    skinning_tools = SkinningTools("hero")
    skinning_tools.auto_skin_mesh("character_mesh", joints)

# 4. 验证绑定
validator = RigValidator("hero")
results = validator.validate_rig()
print(f"绑定质量评分: {results['overall_score']}/100")
```

这个API参考提供了使用角色自动绑定插件所需的所有信息。
