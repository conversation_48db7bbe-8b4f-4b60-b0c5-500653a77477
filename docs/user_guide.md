# 角色自动绑定插件 - 用户指南

## 概述

这个Maya插件提供了全面的角色自动绑定工具，包括骨骼创建、控制器设置、蒙皮和面部绑定。采用行业标准实践和现代Python架构构建。

## 功能特性

### 核心绑定工具
- **自动骨骼创建**：生成完整的角色骨骼，具有规范的命名约定
- **控制系统**：创建动画控制器，支持自定义形状和颜色
- **蒙皮工具**：高级自动蒙皮，包含权重优化和质量检查
- **面部绑定**：全面的面部控制设置，集成BlendShape系统
- **约束工具**：自动约束设置和管理

### 命令
- **autoRigCharacter**：角色自动绑定主命令
- **createCharacterSkeleton**：创建骨骼结构
- **autoSkinCharacter**：应用自动蒙皮

### 质量保证
- **绑定验证**：全面的绑定质量检查
- **命名规范**：标准化命名系统
- **错误检测**：内置验证和错误报告

### 用户界面
- **角色绑定窗口**：分步绑定向导，提供实时反馈

## 安装说明

### 方法1：手动安装

1. 将 `maya_plugin` 文件夹复制到以下位置之一：
   - `Documents/maya/scripts/` (Windows)
   - `~/maya/scripts/` (macOS/Linux)
   - 任何在您的 `PYTHONPATH` 中的目录

2. 在Maya中，打开插件管理器：
   - 转到 **窗口 > 设置/首选项 > 插件管理器**
   - 点击 **浏览** 并导航到 `maya_plugin/plugin.py`
   - 勾选 **已加载** 复选框来加载插件

### 方法2：环境变量

1. 设置 `MAYA_PLUG_IN_PATH` 环境变量：
   ```bash
   export MAYA_PLUG_IN_PATH=/path/to/your/maya_plugin:$MAYA_PLUG_IN_PATH
   ```

2. 设置 `PYTHONPATH` 环境变量：
   ```bash
   export PYTHONPATH=/path/to/your/project:$PYTHONPATH
   ```

3. 重启Maya并从插件管理器加载插件

## 使用方法

### 使用命令

#### 自动绑定命令

`autoRigCharacter` 命令创建完整的角色绑定，包含可自定义的参数。

**基本用法：**
```python
import maya.cmds as cmds

# 创建默认双足绑定
cmds.autoRigCharacter()

# 创建命名的角色绑定，自定义尺寸
cmds.autoRigCharacter(name="hero", scale=2.0)

# 使用详细输出
cmds.autoRigCharacter(verbose=True)
```

**命令标志：**
- `-h, -help`：显示帮助信息
- `-v, -verbose`：启用详细输出
- `-n, -name <string>`：角色名称
- `-s, -scale <float>`：绑定缩放比例
- `-t, -type <string>`：绑定类型（biped, quadruped）
- `-so, -skeletonOnly`：仅创建骨骼
- `-co, -controlsOnly`：仅创建控制器

### Using Nodes

#### Example Node

The `exampleNode` multiplies two input values and outputs the result.

**Creating and using the node:**
```python
import maya.cmds as cmds

# Create the node
node = cmds.createNode("exampleNode")

# Set input values
cmds.setAttr(f"{node}.inputA", 3.0)
cmds.setAttr(f"{node}.inputB", 4.0)

# Get the result (should be 12.0)
result = cmds.getAttr(f"{node}.output")
print(f"Result: {result}")
```

**Node attributes:**
- `inputA`: First input value (double)
- `inputB`: Second input value (double)
- `output`: Result of inputA * inputB (double, read-only)

### Using the User Interface

#### Main Window

The plugin provides a Qt-based user interface for easy interaction.

**Opening the UI:**
```python
from maya_plugin.ui.main_window import show_main_window

# Show the main window
window = show_main_window()
```

**UI Features:**
- **Run Example Command**: Execute the example command
- **Create Example Node**: Create an instance of the example node
- **Test Node Calculation**: Test node computation with custom input values
- **Results Panel**: View command outputs and results

## Utility Functions

The plugin includes utility functions for common Maya operations:

```python
from maya_plugin.utils import maya_utils

# Get Maya version
version = maya_utils.get_maya_version()

# Get selected objects
selection = maya_utils.get_selected_objects()

# Create a locator at a specific position
locator = maya_utils.create_locator_at_position((0, 5, 0), "myLocator")

# Safely delete objects
maya_utils.safe_delete_objects(["object1", "object2"])

# Get/set object world position
position = maya_utils.get_object_world_position("pCube1")
maya_utils.set_object_world_position("pCube1", (1, 2, 3))
```

## Troubleshooting

### Common Issues

**Plugin won't load:**
- Check that the plugin path is correct
- Verify that Python can import the plugin modules
- Check the Maya Script Editor for error messages

**Commands not found:**
- Ensure the plugin is loaded in the Plug-in Manager
- Check that the command names are spelled correctly
- Verify that the plugin registered successfully

**UI won't open:**
- Check that Qt bindings (PySide2/PySide6/PyQt5) are available
- Verify that Maya's UI is properly initialized
- Check for error messages in the Script Editor

### Error Messages

**"No module named 'maya_plugin'":**
- The plugin directory is not in Python's path
- Add the parent directory to `PYTHONPATH`

**"Command 'exampleCommand' not found":**
- The plugin is not loaded
- Load the plugin through the Plug-in Manager

**"Node type 'exampleNode' not found":**
- The plugin is not loaded or failed to register the node
- Check the Script Editor for registration errors

## Development

### Extending the Plugin

To add new functionality:

1. **New Commands**: Add command classes to `maya_plugin/commands/`
2. **New Nodes**: Add node classes to `maya_plugin/nodes/`
3. **New UI**: Add interface components to `maya_plugin/ui/`
4. **Utilities**: Add helper functions to `maya_plugin/utils/`

### Testing

Run the test suite:
```bash
cd /path/to/project
python -m pytest tests/
```

### Building Documentation

Generate documentation using Sphinx:
```bash
cd docs/
sphinx-build -b html . _build/html
```

## API Reference

For detailed API documentation, see the docstrings in the source code or generate the documentation using Sphinx.

## Support

For issues, questions, or contributions:
- Check the project documentation
- Review the example code
- Submit issues or pull requests to the project repository
