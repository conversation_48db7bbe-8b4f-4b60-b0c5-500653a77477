# Character Auto Rigging Plugin - User Guide

## Overview

This Maya plugin provides comprehensive tools for automated character rigging, including skeleton creation, control setup, skinning, and facial rigging. Built with industry-standard practices and modern Python architecture.

## Features

### Core Rigging Tools
- **Automated Skeleton Creation**: Generate complete character skeletons with proper naming conventions
- **Control System**: Create animation controls with customizable shapes and colors
- **Skinning Tools**: Advanced auto-skinning with weight optimization and quality checking
- **Facial Rigging**: Comprehensive facial control setup with BlendShape integration
- **Constraint Tools**: Automated constraint setup and management

### Commands
- **autoRigCharacter**: Main command for automated character rigging
- **createCharacterSkeleton**: Create skeleton structures
- **autoSkinCharacter**: Apply automatic skinning

### Quality Assurance
- **Rig Validation**: Comprehensive rig quality checking
- **Naming Convention**: Standardized naming system
- **Error Detection**: Built-in validation and error reporting

### User Interface
- **Character Rigging Window**: Step-by-step rigging wizard with real-time feedback

## Installation

### Method 1: Manual Installation

1. Copy the `maya_plugin` folder to one of these locations:
   - `Documents/maya/scripts/` (Windows)
   - `~/maya/scripts/` (macOS/Linux)
   - Any directory in your `PYTHONPATH`

2. In Maya, open the Plug-in Manager:
   - Go to **Windows > Settings/Preferences > Plug-in Manager**
   - Click **Browse** and navigate to `maya_plugin/plugin.py`
   - Check the **Loaded** checkbox to load the plugin

### Method 2: Environment Variables

1. Set the `MAYA_PLUG_IN_PATH` environment variable:
   ```bash
   export MAYA_PLUG_IN_PATH=/path/to/your/maya_plugin:$MAYA_PLUG_IN_PATH
   ```

2. Set the `PYTHONPATH` environment variable:
   ```bash
   export PYTHONPATH=/path/to/your/project:$PYTHONPATH
   ```

3. Restart Maya and load the plugin from the Plug-in Manager

## Usage

### Using Commands

#### Example Command

The `exampleCommand` creates a cube with customizable parameters.

**Basic usage:**
```python
import maya.cmds as cmds

# Create a default cube
cmds.exampleCommand()

# Create a named cube with custom size
cmds.exampleCommand(name="myCube", size=2.0)

# Use verbose output
cmds.exampleCommand(verbose=True)
```

**Command flags:**
- `-h, -help`: Display help information
- `-v, -verbose`: Enable verbose output
- `-n, -name <string>`: Name for the created object
- `-s, -size <float>`: Size of the cube

### Using Nodes

#### Example Node

The `exampleNode` multiplies two input values and outputs the result.

**Creating and using the node:**
```python
import maya.cmds as cmds

# Create the node
node = cmds.createNode("exampleNode")

# Set input values
cmds.setAttr(f"{node}.inputA", 3.0)
cmds.setAttr(f"{node}.inputB", 4.0)

# Get the result (should be 12.0)
result = cmds.getAttr(f"{node}.output")
print(f"Result: {result}")
```

**Node attributes:**
- `inputA`: First input value (double)
- `inputB`: Second input value (double)
- `output`: Result of inputA * inputB (double, read-only)

### Using the User Interface

#### Main Window

The plugin provides a Qt-based user interface for easy interaction.

**Opening the UI:**
```python
from maya_plugin.ui.main_window import show_main_window

# Show the main window
window = show_main_window()
```

**UI Features:**
- **Run Example Command**: Execute the example command
- **Create Example Node**: Create an instance of the example node
- **Test Node Calculation**: Test node computation with custom input values
- **Results Panel**: View command outputs and results

## Utility Functions

The plugin includes utility functions for common Maya operations:

```python
from maya_plugin.utils import maya_utils

# Get Maya version
version = maya_utils.get_maya_version()

# Get selected objects
selection = maya_utils.get_selected_objects()

# Create a locator at a specific position
locator = maya_utils.create_locator_at_position((0, 5, 0), "myLocator")

# Safely delete objects
maya_utils.safe_delete_objects(["object1", "object2"])

# Get/set object world position
position = maya_utils.get_object_world_position("pCube1")
maya_utils.set_object_world_position("pCube1", (1, 2, 3))
```

## Troubleshooting

### Common Issues

**Plugin won't load:**
- Check that the plugin path is correct
- Verify that Python can import the plugin modules
- Check the Maya Script Editor for error messages

**Commands not found:**
- Ensure the plugin is loaded in the Plug-in Manager
- Check that the command names are spelled correctly
- Verify that the plugin registered successfully

**UI won't open:**
- Check that Qt bindings (PySide2/PySide6/PyQt5) are available
- Verify that Maya's UI is properly initialized
- Check for error messages in the Script Editor

### Error Messages

**"No module named 'maya_plugin'":**
- The plugin directory is not in Python's path
- Add the parent directory to `PYTHONPATH`

**"Command 'exampleCommand' not found":**
- The plugin is not loaded
- Load the plugin through the Plug-in Manager

**"Node type 'exampleNode' not found":**
- The plugin is not loaded or failed to register the node
- Check the Script Editor for registration errors

## Development

### Extending the Plugin

To add new functionality:

1. **New Commands**: Add command classes to `maya_plugin/commands/`
2. **New Nodes**: Add node classes to `maya_plugin/nodes/`
3. **New UI**: Add interface components to `maya_plugin/ui/`
4. **Utilities**: Add helper functions to `maya_plugin/utils/`

### Testing

Run the test suite:
```bash
cd /path/to/project
python -m pytest tests/
```

### Building Documentation

Generate documentation using Sphinx:
```bash
cd docs/
sphinx-build -b html . _build/html
```

## API Reference

For detailed API documentation, see the docstrings in the source code or generate the documentation using Sphinx.

## Support

For issues, questions, or contributions:
- Check the project documentation
- Review the example code
- Submit issues or pull requests to the project repository
