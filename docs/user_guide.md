# 角色自动绑定插件 - 用户指南

## 概述

这个Maya插件提供了全面的角色自动绑定工具，包括骨骼创建、控制器设置、蒙皮和面部绑定。采用行业标准实践和现代Python架构构建。

## 功能特性

### 核心绑定工具
- **自动骨骼创建**：生成完整的角色骨骼，具有规范的命名约定
- **控制系统**：创建动画控制器，支持自定义形状和颜色
- **蒙皮工具**：高级自动蒙皮，包含权重优化和质量检查
- **面部绑定**：全面的面部控制设置，集成BlendShape系统
- **约束工具**：自动约束设置和管理

### 命令
- **autoRigCharacter**：角色自动绑定主命令
- **createCharacterSkeleton**：创建骨骼结构
- **autoSkinCharacter**：应用自动蒙皮

### 质量保证
- **绑定验证**：全面的绑定质量检查
- **命名规范**：标准化命名系统
- **错误检测**：内置验证和错误报告

### 用户界面
- **角色绑定窗口**：分步绑定向导，提供实时反馈

## 安装说明

### 方法1：手动安装

1. 将 `maya_plugin` 文件夹复制到以下位置之一：
   - `Documents/maya/scripts/` (Windows)
   - `~/maya/scripts/` (macOS/Linux)
   - 任何在您的 `PYTHONPATH` 中的目录

2. 在Maya中，打开插件管理器：
   - 转到 **窗口 > 设置/首选项 > 插件管理器**
   - 点击 **浏览** 并导航到 `maya_plugin/plugin.py`
   - 勾选 **已加载** 复选框来加载插件

### 方法2：环境变量

1. 设置 `MAYA_PLUG_IN_PATH` 环境变量：
   ```bash
   export MAYA_PLUG_IN_PATH=/path/to/your/maya_plugin:$MAYA_PLUG_IN_PATH
   ```

2. 设置 `PYTHONPATH` 环境变量：
   ```bash
   export PYTHONPATH=/path/to/your/project:$PYTHONPATH
   ```

3. 重启Maya并从插件管理器加载插件

## 使用方法

### 使用命令

#### 自动绑定命令

`autoRigCharacter` 命令创建完整的角色绑定，包含可自定义的参数。

**基本用法：**
```python
import maya.cmds as cmds

# 创建默认双足绑定
cmds.autoRigCharacter()

# 创建命名的角色绑定，自定义尺寸
cmds.autoRigCharacter(name="hero", scale=2.0)

# 使用详细输出
cmds.autoRigCharacter(verbose=True)
```

**命令标志：**
- `-h, -help`：显示帮助信息
- `-v, -verbose`：启用详细输出
- `-n, -name <string>`：角色名称
- `-s, -scale <float>`：绑定缩放比例
- `-t, -type <string>`：绑定类型（biped, quadruped）
- `-so, -skeletonOnly`：仅创建骨骼
- `-co, -controlsOnly`：仅创建控制器

### 使用节点

#### 示例节点

`exampleNode` 将两个输入值相乘并输出结果。

**创建和使用节点：**
```python
import maya.cmds as cmds

# 创建节点
node = cmds.createNode("exampleNode")

# 设置输入值
cmds.setAttr(f"{node}.inputA", 3.0)
cmds.setAttr(f"{node}.inputB", 4.0)

# 获取结果（应该是12.0）
result = cmds.getAttr(f"{node}.output")
print(f"结果: {result}")
```

**节点属性：**
- `inputA`：第一个输入值（双精度）
- `inputB`：第二个输入值（双精度）
- `output`：inputA * inputB的结果（双精度，只读）

### 使用用户界面

#### 主窗口

插件提供基于Qt的用户界面，便于交互操作。

**打开UI：**
```python
from maya_plugin.ui.main_window import show_rigging_window

# 显示主窗口
window = show_rigging_window()
```

**UI功能：**
- **运行自动绑定命令**：执行角色自动绑定
- **创建骨骼**：创建角色骨骼结构
- **创建控制器**：为骨骼生成动画控制器
- **应用蒙皮**：自动蒙皮和权重优化
- **面部绑定**：设置面部控制和表情
- **验证绑定**：检查绑定质量和错误
- **结果面板**：查看命令输出和结果

## 实用函数

插件包含用于常见Maya操作的实用函数：

```python
from maya_plugin.utils import maya_utils

# 获取Maya版本
version = maya_utils.get_maya_version()

# 获取选中对象
selection = maya_utils.get_selected_objects()

# 在指定位置创建定位器
locator = maya_utils.create_locator_at_position((0, 5, 0), "myLocator")

# 安全删除对象
maya_utils.safe_delete_objects(["object1", "object2"])

# 获取/设置对象世界位置
position = maya_utils.get_object_world_position("pCube1")
maya_utils.set_object_world_position("pCube1", (1, 2, 3))
```

## 故障排除

### 常见问题

**插件无法加载：**
- 检查插件路径是否正确
- 验证Python可以导入插件模块
- 查看Maya脚本编辑器中的错误信息

**命令未找到：**
- 确保插件在插件管理器中已加载
- 检查命令名称拼写是否正确
- 验证插件注册是否成功

**UI无法打开：**
- 检查Qt绑定库（PySide2/PySide6/PyQt5）是否可用
- 验证Maya的UI已正确初始化
- 查看脚本编辑器中的错误信息

### 错误信息

**"No module named 'maya_plugin'"：**
- 插件目录不在Python路径中
- 将父目录添加到 `PYTHONPATH`

**"Command 'autoRigCharacter' not found"：**
- 插件未加载
- 通过插件管理器加载插件

**"Node type 'exampleNode' not found"：**
- 插件未加载或节点注册失败
- 查看脚本编辑器中的注册错误

## 开发

### 扩展插件

要添加新功能：

1. **新命令**：将命令类添加到 `maya_plugin/commands/`
2. **新节点**：将节点类添加到 `maya_plugin/nodes/`
3. **新UI**：将界面组件添加到 `maya_plugin/ui/`
4. **实用工具**：将辅助函数添加到 `maya_plugin/utils/`

### 测试

运行测试套件：
```bash
cd /path/to/project
python -m pytest tests/
```

### 构建文档

使用Sphinx生成文档：
```bash
cd docs/
sphinx-build -b html . _build/html
```

## API参考

有关详细的API文档，请参阅源代码中的文档字符串或使用Sphinx生成文档。
也可以查看 `docs/api_reference.md` 获取完整的中文API参考。

## 支持

如有问题、疑问或贡献：
- 查看项目文档
- 运行 `examples/test_imports.py` 诊断问题
- 查阅示例代码
- 向项目仓库提交问题或拉取请求

## 高级用法

### 自定义骨骼模板

您可以创建自定义骨骼模板：

```python
from maya_plugin.rigging.skeleton import SkeletonBuilder

# 创建自定义骨骼构建器
skeleton_builder = SkeletonBuilder("custom_character", "biped")

# 添加自定义关节
skeleton_builder.add_custom_joint("extra_spine", (0, 14.5, 0), parent="spine_03")

# 创建完整骨骼
joints = skeleton_builder.create_skeleton(scale=1.5)
```

### 批量角色绑定

对多个角色进行批量绑定：

```python
import maya.cmds as cmds

characters = ["hero", "villain", "sidekick"]

for char_name in characters:
    # 为每个角色创建绑定
    result = cmds.autoRigCharacter(
        name=char_name,
        type="biped",
        scale=1.0,
        verbose=True
    )
    print(f"已完成 {char_name} 的绑定")
```

### 绑定质量检查

定期检查绑定质量：

```python
from maya_plugin.utils.validation import RigValidator

# 创建验证器
validator = RigValidator("character_name")

# 运行验证
results = validator.validate_rig()

# 检查评分
if results["overall_score"] < 80:
    print("绑定质量需要改进")
    print(validator.get_validation_report())
```
