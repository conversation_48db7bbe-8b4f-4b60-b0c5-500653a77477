#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误修复脚本

此脚本检查并修复角色自动绑定插件中的常见错误。
"""

import os
import sys
import re


def check_python_syntax(file_path):
    """检查Python文件的语法错误。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 编译检查语法
        compile(content, file_path, 'exec')
        return True, None
    except SyntaxError as e:
        return False, f"语法错误在第{e.lineno}行: {e.msg}"
    except Exception as e:
        return False, f"检查错误: {str(e)}"


def check_import_statements(file_path):
    """检查导入语句的问题。"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # 检查相对导入
            if line.startswith('from ..') or line.startswith('from .'):
                # 确保相对导入格式正确
                if not re.match(r'from \.\.[a-zA-Z_][a-zA-Z0-9_.]*', line) and \
                   not re.match(r'from \.[a-zA-Z_][a-zA-Z0-9_.]*', line):
                    issues.append(f"第{i}行: 相对导入格式可能有问题: {line}")
            
            # 检查Maya导入
            if 'import maya' in line:
                if 'maya.cmds' not in line and 'maya.api' not in line and 'maya.standalone' not in line:
                    issues.append(f"第{i}行: 可能的Maya导入问题: {line}")
    
    except Exception as e:
        issues.append(f"检查导入时出错: {str(e)}")
    
    return issues


def check_method_definitions(file_path):
    """检查方法定义的问题。"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查缺少的方法实现
        method_declarations = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
        
        for method in method_declarations:
            # 检查方法是否有实现（不只是pass）
            method_pattern = rf'def\s+{method}\s*\([^)]*\):(.*?)(?=def\s+\w+|class\s+\w+|\Z)'
            match = re.search(method_pattern, content, re.DOTALL)
            
            if match:
                method_body = match.group(1).strip()
                if not method_body or method_body == 'pass':
                    issues.append(f"方法 '{method}' 可能缺少实现")
    
    except Exception as e:
        issues.append(f"检查方法定义时出错: {str(e)}")
    
    return issues


def fix_common_issues(file_path):
    """修复常见问题。"""
    fixes_applied = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复常见的导入问题
        # 确保Maya导入在try-except块中
        if 'import maya.cmds as cmds' in content and 'try:' not in content:
            content = content.replace(
                'import maya.cmds as cmds',
                'try:\n    import maya.cmds as cmds\nexcept ImportError:\n    print("Maya not available")'
            )
            fixes_applied.append("添加了Maya导入的错误处理")
        
        # 修复缺少的__init__.py文件
        if file_path.endswith('__init__.py') and not content.strip():
            content = '"""Package initialization."""\n'
            fixes_applied.append("添加了包初始化内容")
        
        # 保存修复后的内容
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
    
    except Exception as e:
        fixes_applied.append(f"修复时出错: {str(e)}")
    
    return fixes_applied


def scan_directory(directory):
    """扫描目录中的Python文件。"""
    python_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files


def main():
    """主函数。"""
    print("角色自动绑定插件 - 错误检查和修复工具")
    print("=" * 50)
    
    # 扫描maya_plugin目录
    plugin_dir = "maya_plugin"
    if not os.path.exists(plugin_dir):
        print(f"错误: 找不到目录 {plugin_dir}")
        return
    
    python_files = scan_directory(plugin_dir)
    print(f"找到 {len(python_files)} 个Python文件")
    
    total_issues = 0
    total_fixes = 0
    
    for file_path in python_files:
        print(f"\n检查文件: {file_path}")
        
        # 检查语法
        syntax_ok, syntax_error = check_python_syntax(file_path)
        if not syntax_ok:
            print(f"  ❌ 语法错误: {syntax_error}")
            total_issues += 1
        else:
            print(f"  ✅ 语法检查通过")
        
        # 检查导入
        import_issues = check_import_statements(file_path)
        if import_issues:
            print(f"  ⚠️  导入问题:")
            for issue in import_issues:
                print(f"    - {issue}")
            total_issues += len(import_issues)
        else:
            print(f"  ✅ 导入检查通过")
        
        # 检查方法定义
        method_issues = check_method_definitions(file_path)
        if method_issues:
            print(f"  ⚠️  方法问题:")
            for issue in method_issues:
                print(f"    - {issue}")
            total_issues += len(method_issues)
        else:
            print(f"  ✅ 方法检查通过")
        
        # 应用修复
        fixes = fix_common_issues(file_path)
        if fixes:
            print(f"  🔧 应用的修复:")
            for fix in fixes:
                print(f"    - {fix}")
            total_fixes += len(fixes)
    
    print("\n" + "=" * 50)
    print(f"检查完成:")
    print(f"  - 发现问题: {total_issues}")
    print(f"  - 应用修复: {total_fixes}")
    
    if total_issues == 0:
        print("🎉 所有文件检查通过！")
    else:
        print("⚠️  仍有问题需要手动修复")
    
    # 额外的建议
    print("\n建议:")
    print("1. 在Maya中测试插件加载")
    print("2. 运行 examples/test_imports.py 进行导入测试")
    print("3. 检查Maya脚本编辑器中的错误信息")
    print("4. 确保所有依赖项已正确安装")


if __name__ == "__main__":
    main()
