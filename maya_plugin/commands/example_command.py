"""
示例Maya命令

此模块演示如何使用Maya API 2.0创建自定义Maya命令。
"""

import maya.api.OpenMaya as om
import maya.cmds as cmds


class ExampleCommand(om.MPxCommand):
    """
    演示基本命令功能的示例Maya命令。

    此命令创建一个简单的立方体并打印消息。
    """

    COMMAND_NAME = "exampleCommand"

    # 命令标志
    HELP_FLAG = ["-h", "-help"]
    VERBOSE_FLAG = ["-v", "-verbose"]
    NAME_FLAG = ["-n", "-name"]
    SIZE_FLAG = ["-s", "-size"]

    def __init__(self):
        """初始化命令。"""
        super(ExampleCommand, self).__init__()

        # 命令参数
        self.verbose = False
        self.object_name = "exampleCube"
        self.size = 1.0
    
    @staticmethod
    def creator():
        """
        创建命令实例。

        Returns:
            ExampleCommand: 命令的新实例
        """
        return ExampleCommand()

    @staticmethod
    def newSyntax():
        """
        定义命令语法和标志。

        Returns:
            MSyntax: 命令语法对象
        """
        syntax = om.MSyntax()

        # 添加标志
        syntax.addFlag(
            ExampleCommand.HELP_FLAG[0],
            ExampleCommand.HELP_FLAG[1],
            om.MSyntax.kNoArg
        )
        
        syntax.addFlag(
            ExampleCommand.VERBOSE_FLAG[0],
            ExampleCommand.VERBOSE_FLAG[1],
            om.MSyntax.kNoArg
        )
        
        syntax.addFlag(
            ExampleCommand.NAME_FLAG[0],
            ExampleCommand.NAME_FLAG[1],
            om.MSyntax.kString
        )
        
        syntax.addFlag(
            ExampleCommand.SIZE_FLAG[0],
            ExampleCommand.SIZE_FLAG[1],
            om.MSyntax.kDouble
        )
        
        return syntax
    
    def doIt(self, args):
        """
        执行命令。

        Args:
            args: 包含命令参数的MArgList
        """
        try:
            # 解析参数
            arg_parser = om.MArgParser(self.syntax(), args)

            # 检查帮助标志
            if arg_parser.isFlagSet(self.HELP_FLAG[0]):
                self.display_help()
                return

            # 解析标志
            if arg_parser.isFlagSet(self.VERBOSE_FLAG[0]):
                self.verbose = True

            if arg_parser.isFlagSet(self.NAME_FLAG[0]):
                self.object_name = arg_parser.flagArgumentString(self.NAME_FLAG[0], 0)

            if arg_parser.isFlagSet(self.SIZE_FLAG[0]):
                self.size = arg_parser.flagArgumentDouble(self.SIZE_FLAG[0], 0)

            # 执行主要命令逻辑
            self.redoIt()

        except Exception as e:
            om.MGlobal.displayError(f"{self.COMMAND_NAME} 中的错误: {str(e)}")
            raise
    
    def redoIt(self):
        """
        重做命令（实际命令执行）。
        此方法包含主要命令逻辑。
        """
        try:
            if self.verbose:
                om.MGlobal.displayInfo(f"创建立方体，名称: {self.object_name}，尺寸: {self.size}")

            # 使用Maya命令创建立方体
            cube_transform, cube_shape = cmds.polyCube(
                name=self.object_name,
                width=self.size,
                height=self.size,
                depth=self.size
            )

            # 存储结果以供撤销
            self.created_objects = [cube_transform, cube_shape]

            # 设置命令结果
            self.setResult(cube_transform)

            # 显示成功消息
            message = f"成功创建立方体: {cube_transform}"
            if self.verbose:
                om.MGlobal.displayInfo(message)
            else:
                print(message)

        except Exception as e:
            om.MGlobal.displayError(f"创建立方体失败: {str(e)}")
            raise
    
    def undoIt(self):
        """
        撤销命令。
        此方法撤销命令的效果。
        """
        try:
            if hasattr(self, 'created_objects'):
                for obj in self.created_objects:
                    if cmds.objExists(obj):
                        cmds.delete(obj)
                        if self.verbose:
                            om.MGlobal.displayInfo(f"已删除对象: {obj}")

        except Exception as e:
            om.MGlobal.displayError(f"撤销 {self.COMMAND_NAME} 失败: {str(e)}")
            raise

    def isUndoable(self):
        """
        指定此命令可撤销。

        Returns:
            bool: 如果命令可以撤销则返回True
        """
        return True
    
    def display_help(self):
        """显示命令的帮助信息。"""
        help_text = f"""
{self.COMMAND_NAME} - 示例Maya命令

用法:
    {self.COMMAND_NAME} [标志]

标志:
    -h  -help                   显示此帮助消息
    -v  -verbose                启用详细输出
    -n  -name     <字符串>      创建对象的名称（默认: "exampleCube"）
    -s  -size     <浮点数>      立方体的尺寸（默认: 1.0）

示例:
    {self.COMMAND_NAME}                           # 创建默认立方体
    {self.COMMAND_NAME} -n "myCube" -s 2.0        # 创建尺寸为2.0的指定名称立方体
    {self.COMMAND_NAME} -v                        # 创建立方体并显示详细输出
"""
        om.MGlobal.displayInfo(help_text)
