"""
自动绑定命令

此模块提供角色自动绑定的主要命令。
"""

import maya.api.OpenMaya as om
import maya.cmds as cmds
from ..rigging.skeleton import SkeletonBuilder
from ..rigging.controls import ControlBuilder
from ..utils.naming import NamingConvention


class AutoRigCommand(om.MPxCommand):
    """
    角色自动绑定的主要命令。

    此命令创建完整的角色绑定，包括骨骼、控制器和约束。
    """
    
    COMMAND_NAME = "autoRigCharacter"

    # 命令标志
    HELP_FLAG = ["-h", "-help"]
    MESH_FLAG = ["-m", "-mesh"]
    CHARACTER_NAME_FLAG = ["-n", "-name"]
    RIG_TYPE_FLAG = ["-t", "-type"]
    SCALE_FLAG = ["-s", "-scale"]
    VERBOSE_FLAG = ["-v", "-verbose"]
    SKELETON_ONLY_FLAG = ["-so", "-skeletonOnly"]
    CONTROLS_ONLY_FLAG = ["-co", "-controlsOnly"]

    def __init__(self):
        """初始化命令。"""
        super(AutoRigCommand, self).__init__()

        # 命令参数
        self.mesh = None
        self.character_name = "character"
        self.rig_type = "biped"
        self.scale = 1.0
        self.verbose = False
        self.skeleton_only = False
        self.controls_only = False

        # 用于撤销的已创建对象
        self.created_objects = []
        self.skeleton_builder = None
        self.control_builder = None
    
    @staticmethod
    def creator():
        """
        创建命令实例。

        Returns:
            AutoRigCommand: 命令的新实例
        """
        return AutoRigCommand()

    @staticmethod
    def newSyntax():
        """
        定义命令语法和标志。

        Returns:
            MSyntax: 命令语法对象
        """
        syntax = om.MSyntax()

        # 添加标志
        syntax.addFlag(
            AutoRigCommand.HELP_FLAG[0],
            AutoRigCommand.HELP_FLAG[1],
            om.MSyntax.kNoArg
        )
        
        syntax.addFlag(
            AutoRigCommand.MESH_FLAG[0],
            AutoRigCommand.MESH_FLAG[1],
            om.MSyntax.kString
        )
        
        syntax.addFlag(
            AutoRigCommand.CHARACTER_NAME_FLAG[0],
            AutoRigCommand.CHARACTER_NAME_FLAG[1],
            om.MSyntax.kString
        )
        
        syntax.addFlag(
            AutoRigCommand.RIG_TYPE_FLAG[0],
            AutoRigCommand.RIG_TYPE_FLAG[1],
            om.MSyntax.kString
        )
        
        syntax.addFlag(
            AutoRigCommand.SCALE_FLAG[0],
            AutoRigCommand.SCALE_FLAG[1],
            om.MSyntax.kDouble
        )
        
        syntax.addFlag(
            AutoRigCommand.VERBOSE_FLAG[0],
            AutoRigCommand.VERBOSE_FLAG[1],
            om.MSyntax.kNoArg
        )
        
        syntax.addFlag(
            AutoRigCommand.SKELETON_ONLY_FLAG[0],
            AutoRigCommand.SKELETON_ONLY_FLAG[1],
            om.MSyntax.kNoArg
        )
        
        syntax.addFlag(
            AutoRigCommand.CONTROLS_ONLY_FLAG[0],
            AutoRigCommand.CONTROLS_ONLY_FLAG[1],
            om.MSyntax.kNoArg
        )
        
        return syntax
    
    def doIt(self, args):
        """
        Execute the command.
        
        Args:
            args: MArgList containing command arguments
        """
        try:
            # Parse arguments
            arg_parser = om.MArgParser(self.syntax(), args)
            
            # Check for help flag
            if arg_parser.isFlagSet(self.HELP_FLAG[0]):
                self.display_help()
                return
            
            # Parse flags
            if arg_parser.isFlagSet(self.MESH_FLAG[0]):
                self.mesh = arg_parser.flagArgumentString(self.MESH_FLAG[0], 0)
            
            if arg_parser.isFlagSet(self.CHARACTER_NAME_FLAG[0]):
                self.character_name = arg_parser.flagArgumentString(self.CHARACTER_NAME_FLAG[0], 0)
            
            if arg_parser.isFlagSet(self.RIG_TYPE_FLAG[0]):
                self.rig_type = arg_parser.flagArgumentString(self.RIG_TYPE_FLAG[0], 0)
            
            if arg_parser.isFlagSet(self.SCALE_FLAG[0]):
                self.scale = arg_parser.flagArgumentDouble(self.SCALE_FLAG[0], 0)
            
            if arg_parser.isFlagSet(self.VERBOSE_FLAG[0]):
                self.verbose = True
            
            if arg_parser.isFlagSet(self.SKELETON_ONLY_FLAG[0]):
                self.skeleton_only = True
            
            if arg_parser.isFlagSet(self.CONTROLS_ONLY_FLAG[0]):
                self.controls_only = True
            
            # Validate parameters
            self._validate_parameters()
            
            # Execute the main command logic
            self.redoIt()
            
        except Exception as e:
            om.MGlobal.displayError(f"Error in {self.COMMAND_NAME}: {str(e)}")
            raise
    
    def _validate_parameters(self):
        """Validate command parameters."""
        # Check if mesh exists (if provided)
        if self.mesh and not cmds.objExists(self.mesh):
            raise ValueError(f"Mesh does not exist: {self.mesh}")
        
        # Validate rig type
        valid_rig_types = ["biped", "quadruped"]
        if self.rig_type not in valid_rig_types:
            raise ValueError(f"Invalid rig type: {self.rig_type}. Valid types: {valid_rig_types}")
        
        # Validate scale
        if self.scale <= 0:
            raise ValueError("Scale must be greater than 0")
        
        # Check for conflicting flags
        if self.skeleton_only and self.controls_only:
            raise ValueError("Cannot use both -skeletonOnly and -controlsOnly flags")
    
    def redoIt(self):
        """
        Redo the command (actual command execution).
        This method contains the main command logic.
        """
        try:
            if self.verbose:
                om.MGlobal.displayInfo(f"Starting auto-rig for character: {self.character_name}")
            
            # Clear created objects list
            self.created_objects.clear()
            
            # Initialize builders
            self.skeleton_builder = SkeletonBuilder(self.character_name, self.rig_type)
            self.control_builder = ControlBuilder(self.character_name)
            
            # Create skeleton (unless controls only)
            skeleton_joints = []
            if not self.controls_only:
                skeleton_joints = self._create_skeleton()
            
            # Create controls (unless skeleton only)
            controls = []
            if not self.skeleton_only:
                controls = self._create_controls(skeleton_joints)
            
            # Set up constraints and connections
            if skeleton_joints and controls:
                self._setup_constraints(skeleton_joints, controls)
            
            # Organize hierarchy
            self._organize_hierarchy()
            
            # Set command result
            result = {
                "character": self.character_name,
                "skeleton": skeleton_joints,
                "controls": controls,
                "created_objects": self.created_objects
            }
            self.setResult(str(result))
            
            # Display success message
            message = f"Successfully created {self.rig_type} rig for {self.character_name}"
            if self.verbose:
                om.MGlobal.displayInfo(message)
            else:
                print(message)
                
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create auto-rig: {str(e)}")
            raise
    
    def _create_skeleton(self):
        """Create the character skeleton."""
        if self.verbose:
            om.MGlobal.displayInfo("Creating skeleton...")
        
        skeleton_joints = self.skeleton_builder.create_skeleton(self.scale)
        self.created_objects.extend(skeleton_joints)
        
        return skeleton_joints
    
    def _create_controls(self, skeleton_joints):
        """Create animation controls."""
        if self.verbose:
            om.MGlobal.displayInfo("Creating controls...")
        
        controls = []
        
        # Create controls for main joints
        main_joints = ["root", "spine_01", "spine_02", "spine_03", "neck_01", "head"]
        
        for joint_name in main_joints:
            joint = self.skeleton_builder.get_joint(joint_name)
            if joint:
                try:
                    control, group = self.control_builder.create_control_for_joint(
                        joint, 
                        shape="circle", 
                        color="blue", 
                        size=self.scale * 2.0
                    )
                    controls.append(control)
                    self.created_objects.extend([control, group])
                except Exception as e:
                    if self.verbose:
                        om.MGlobal.displayWarning(f"Failed to create control for {joint_name}: {str(e)}")
        
        # Create arm controls
        arm_joints = ["clavicle_L", "upperarm_L", "lowerarm_L", "hand_L",
                     "clavicle_R", "upperarm_R", "lowerarm_R", "hand_R"]
        
        for joint_name in arm_joints:
            joint = self.skeleton_builder.get_joint(joint_name)
            if joint:
                try:
                    side = "left" if "_L" in joint_name else "right"
                    color = "red" if side == "left" else "blue"
                    
                    control, group = self.control_builder.create_control_for_joint(
                        joint,
                        shape="circle",
                        color=color,
                        size=self.scale * 1.5
                    )
                    controls.append(control)
                    self.created_objects.extend([control, group])
                except Exception as e:
                    if self.verbose:
                        om.MGlobal.displayWarning(f"Failed to create control for {joint_name}: {str(e)}")
        
        return controls
    
    def _setup_constraints(self, skeleton_joints, controls):
        """Set up constraints between controls and joints."""
        if self.verbose:
            om.MGlobal.displayInfo("Setting up constraints...")
        
        # Constrain joints to controls
        for control_name in self.control_builder.get_all_controls():
            control = self.control_builder.get_control(control_name)
            
            # Find corresponding joint
            joint_name = control_name.replace("_ctrl", "")
            joint = self.skeleton_builder.get_joint(joint_name)
            
            if joint and control:
                try:
                    constraint = cmds.parentConstraint(control, joint, maintainOffset=True)[0]
                    self.created_objects.append(constraint)
                except Exception as e:
                    if self.verbose:
                        om.MGlobal.displayWarning(f"Failed to constrain {joint} to {control}: {str(e)}")
    
    def _organize_hierarchy(self):
        """Organize the rig hierarchy."""
        if self.verbose:
            om.MGlobal.displayInfo("Organizing hierarchy...")
        
        naming = NamingConvention(self.character_name)
        
        # Create main groups
        rig_group = cmds.group(empty=True, name=naming.get_group_name("rig", group_type="main"))
        skeleton_group = cmds.group(empty=True, name=naming.get_group_name("skeleton"))
        controls_group = cmds.group(empty=True, name=naming.get_group_name("controls"))
        
        # Parent groups to main rig group
        cmds.parent(skeleton_group, controls_group, rig_group)
        
        # Parent skeleton joints to skeleton group
        root_joint = self.skeleton_builder.get_joint("root")
        if root_joint:
            cmds.parent(root_joint, skeleton_group)
        
        # Parent control groups to controls group
        for control_name in self.control_builder.get_all_controls():
            control_group = self.control_builder.get_control_group(control_name)
            if control_group:
                try:
                    cmds.parent(control_group, controls_group)
                except:
                    pass  # May already be parented
        
        self.created_objects.extend([rig_group, skeleton_group, controls_group])
    
    def undoIt(self):
        """
        Undo the command.
        This method reverses the command's effects.
        """
        try:
            for obj in reversed(self.created_objects):
                if cmds.objExists(obj):
                    cmds.delete(obj)
                    if self.verbose:
                        om.MGlobal.displayInfo(f"Deleted object: {obj}")
            
            self.created_objects.clear()
                        
        except Exception as e:
            om.MGlobal.displayError(f"Failed to undo {self.COMMAND_NAME}: {str(e)}")
            raise
    
    def isUndoable(self):
        """
        Specify that this command is undoable.
        
        Returns:
            bool: True if the command can be undone
        """
        return True
    
    def display_help(self):
        """Display help information for the command."""
        help_text = f"""
{self.COMMAND_NAME} - Automated Character Rigging Command

Usage:
    {self.COMMAND_NAME} [flags]

Flags:
    -h   -help                      Display this help message
    -m   -mesh        <string>      Character mesh to rig (optional)
    -n   -name        <string>      Character name (default: "character")
    -t   -type        <string>      Rig type: biped, quadruped (default: "biped")
    -s   -scale       <float>       Rig scale factor (default: 1.0)
    -v   -verbose                   Enable verbose output
    -so  -skeletonOnly              Create skeleton only
    -co  -controlsOnly              Create controls only (requires existing skeleton)

Examples:
    {self.COMMAND_NAME}                                    # Create default biped rig
    {self.COMMAND_NAME} -n "hero" -t "biped" -s 2.0       # Create scaled biped rig
    {self.COMMAND_NAME} -m "characterMesh" -v              # Rig with mesh reference
    {self.COMMAND_NAME} -so                                # Create skeleton only
"""
        om.MGlobal.displayInfo(help_text)
