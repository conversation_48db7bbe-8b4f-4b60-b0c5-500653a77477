"""
Character Auto Rigging Plugin Entry Point

This is the main plugin file that <PERSON> will load.
It handles plugin initialization, registration of commands and nodes.
"""

import sys
import maya.api.OpenMaya as om
import maya.cmds as cmds

# Import plugin components
from .commands.auto_rig_command import AutoRigCommand
from .commands.example_command import ExampleCommand
from .nodes.example_node import ExampleNode

# Plugin information
PLUGIN_NAME = "Character Auto Rigging"
PLUGIN_VERSION = "1.0.0"
PLUGIN_AUTHOR = "Character Rigging Team"
PLUGIN_VENDOR = "Rigging Studio"

# Maya API version
maya_useNewAPI = True


def initializePlugin(plugin):
    """
    Initialize the plugin when <PERSON> loads it.
    
    Args:
        plugin: MObject representing the plugin
    """
    vendor = PLUGIN_VENDOR
    version = PLUGIN_VERSION
    
    # Get plugin function set
    plugin_fn = om.MFnPlugin(plugin, vendor, version)
    
    try:
        # Register commands
        plugin_fn.registerCommand(
            AutoRigCommand.COMMAND_NAME,
            AutoRigCommand.creator,
            AutoRigCommand.newSyntax
        )

        plugin_fn.registerCommand(
            ExampleCommand.COMMAND_NAME,
            ExampleCommand.creator,
            ExampleCommand.newSyntax
        )

        # Register nodes
        plugin_fn.registerNode(
            ExampleNode.NODE_NAME,
            ExampleNode.NODE_ID,
            ExampleNode.creator,
            ExampleNode.initialize,
            om.MPxNode.kDependNode
        )
        
        # Print success message
        om.MGlobal.displayInfo(f"Successfully loaded {PLUGIN_NAME} v{PLUGIN_VERSION}")
        
    except Exception as e:
        om.MGlobal.displayError(f"Failed to register {PLUGIN_NAME}: {str(e)}")
        raise


def uninitializePlugin(plugin):
    """
    Clean up the plugin when Maya unloads it.
    
    Args:
        plugin: MObject representing the plugin
    """
    # Get plugin function set
    plugin_fn = om.MFnPlugin(plugin)
    
    try:
        # Deregister commands
        plugin_fn.deregisterCommand(AutoRigCommand.COMMAND_NAME)
        plugin_fn.deregisterCommand(ExampleCommand.COMMAND_NAME)

        # Deregister nodes
        plugin_fn.deregisterNode(ExampleNode.NODE_ID)
        
        # Print success message
        om.MGlobal.displayInfo(f"Successfully unloaded {PLUGIN_NAME}")
        
    except Exception as e:
        om.MGlobal.displayError(f"Failed to unregister {PLUGIN_NAME}: {str(e)}")
        raise


# Optional: Plugin menu creation
def create_plugin_menu():
    """Create a menu for the plugin in Maya's main menu bar."""
    menu_name = f"{PLUGIN_NAME}Menu"
    
    # Check if menu already exists
    if cmds.menu(menu_name, exists=True):
        cmds.deleteUI(menu_name, menu=True)
    
    # Create main menu
    main_menu = cmds.menu(
        menu_name,
        label=PLUGIN_NAME,
        parent="MayaWindow",
        tearOff=True
    )
    
    # Add menu items
    cmds.menuItem(
        label="Auto Rig Character",
        command=f"cmds.{AutoRigCommand.COMMAND_NAME}()",
        parent=main_menu
    )

    cmds.menuItem(divider=True, parent=main_menu)

    cmds.menuItem(
        label="Open Rigging UI",
        command="from maya_plugin.ui.main_window import show_rigging_window; show_rigging_window()",
        parent=main_menu
    )

    cmds.menuItem(divider=True, parent=main_menu)

    cmds.menuItem(
        label="Example Command",
        command=f"cmds.{ExampleCommand.COMMAND_NAME}()",
        parent=main_menu
    )

    cmds.menuItem(
        label="Create Example Node",
        command=f"cmds.createNode('{ExampleNode.NODE_NAME}')",
        parent=main_menu
    )
    
    cmds.menuItem(divider=True, parent=main_menu)
    
    cmds.menuItem(
        label="About",
        command=f"cmds.confirmDialog(title='About {PLUGIN_NAME}', "
                f"message='{PLUGIN_NAME} v{PLUGIN_VERSION}\\nBy {PLUGIN_AUTHOR}', "
                f"button=['OK'])",
        parent=main_menu
    )


def remove_plugin_menu():
    """Remove the plugin menu from Maya's main menu bar."""
    menu_name = f"{PLUGIN_NAME}Menu"
    if cmds.menu(menu_name, exists=True):
        cmds.deleteUI(menu_name, menu=True)


# Auto-create menu when plugin loads (optional)
# Uncomment the following lines if you want automatic menu creation
# try:
#     create_plugin_menu()
# except:
#     pass
