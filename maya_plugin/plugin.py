"""
Character Auto Rigging Plugin Entry Point

This is the main plugin file that <PERSON> will load.
It handles plugin initialization, registration of commands and nodes.
"""

import sys
import maya.api.OpenMaya as om
import maya.cmds as cmds

# Import plugin components
from .commands.auto_rig_command import AutoRigCommand
from .commands.example_command import ExampleCommand
from .nodes.example_node import ExampleNode

# Plugin information
PLUGIN_NAME = "Character Auto Rigging"
PLUGIN_VERSION = "1.0.0"
PLUGIN_AUTHOR = "Character Rigging Team"
PLUGIN_VENDOR = "Rigging Studio"

# Maya API version
maya_useNewAPI = True


def initializePlugin(plugin):
    """
    Initialize the plugin when <PERSON> loads it.
    
    Args:
        plugin: MObject representing the plugin
    """
    vendor = PLUGIN_VENDOR
    version = PLUGIN_VERSION
    
    # Get plugin function set
    plugin_fn = om.MFnPlugin(plugin, vendor, version)
    
    try:
        # Register commands
        plugin_fn.registerCommand(
            AutoRigCommand.COMMAND_NAME,
            AutoRigCommand.creator,
            AutoRigCommand.newSyntax
        )

        plugin_fn.registerCommand(
            ExampleCommand.COMMAND_NAME,
            ExampleCommand.creator,
            ExampleCommand.newSyntax
        )

        # Register nodes
        plugin_fn.registerNode(
            ExampleNode.NODE_NAME,
            ExampleNode.NODE_ID,
            ExampleNode.creator,
            ExampleNode.initialize,
            om.MPxNode.kDependNode
        )
        
        # Print success message
        om.MGlobal.displayInfo(f"Successfully loaded {PLUGIN_NAME} v{PLUGIN_VERSION}")
        
    except Exception as e:
        om.MGlobal.displayError(f"Failed to register {PLUGIN_NAME}: {str(e)}")
        raise


def uninitializePlugin(plugin):
    """
    Clean up the plugin when Maya unloads it.
    
    Args:
        plugin: MObject representing the plugin
    """
    # Get plugin function set
    plugin_fn = om.MFnPlugin(plugin)
    
    try:
        # Deregister commands
        plugin_fn.deregisterCommand(AutoRigCommand.COMMAND_NAME)
        plugin_fn.deregisterCommand(ExampleCommand.COMMAND_NAME)

        # Deregister nodes
        plugin_fn.deregisterNode(ExampleNode.NODE_ID)
        
        # Print success message
        om.MGlobal.displayInfo(f"Successfully unloaded {PLUGIN_NAME}")
        
    except Exception as e:
        om.MGlobal.displayError(f"Failed to unregister {PLUGIN_NAME}: {str(e)}")
        raise


# 可选：插件菜单创建
def create_plugin_menu():
    """在Maya主菜单栏中为插件创建菜单。"""
    menu_name = f"{PLUGIN_NAME}Menu"

    # 检查菜单是否已存在
    if cmds.menu(menu_name, exists=True):
        cmds.deleteUI(menu_name, menu=True)

    # 创建主菜单
    main_menu = cmds.menu(
        menu_name,
        label=PLUGIN_NAME,
        parent="MayaWindow",
        tearOff=True
    )

    # 添加菜单项
    cmds.menuItem(
        label="自动绑定角色",
        command=f"cmds.{AutoRigCommand.COMMAND_NAME}()",
        parent=main_menu
    )

    cmds.menuItem(divider=True, parent=main_menu)

    cmds.menuItem(
        label="打开绑定界面",
        command="from maya_plugin.ui.main_window import show_rigging_window; show_rigging_window()",
        parent=main_menu
    )

    cmds.menuItem(divider=True, parent=main_menu)

    cmds.menuItem(
        label="示例命令",
        command=f"cmds.{ExampleCommand.COMMAND_NAME}()",
        parent=main_menu
    )

    cmds.menuItem(
        label="创建示例节点",
        command=f"cmds.createNode('{ExampleNode.NODE_NAME}')",
        parent=main_menu
    )

    cmds.menuItem(divider=True, parent=main_menu)

    cmds.menuItem(
        label="关于",
        command=f"cmds.confirmDialog(title='关于 {PLUGIN_NAME}', "
                f"message='{PLUGIN_NAME} v{PLUGIN_VERSION}\\n作者：{PLUGIN_AUTHOR}', "
                f"button=['确定'])",
        parent=main_menu
    )


def remove_plugin_menu():
    """从Maya主菜单栏中移除插件菜单。"""
    menu_name = f"{PLUGIN_NAME}Menu"
    if cmds.menu(menu_name, exists=True):
        cmds.deleteUI(menu_name, menu=True)


# 插件加载时自动创建菜单（可选）
# 如果您希望自动创建菜单，请取消注释以下行
# try:
#     create_plugin_menu()
# except:
#     pass
