"""
Validation Tools Module

This module provides quality assurance and validation tools for character rigs.
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om
from .naming import NamingConvention


class RigValidator:
    """
    Main class for rig validation and quality assurance.
    """
    
    def __init__(self, character_name="character"):
        """
        Initialize the rig validator.
        
        Args:
            character_name (str): Name prefix for the character
        """
        self.character_name = character_name
        self.naming = NamingConvention(character_name)
        self.validation_results = {}
    
    def validate_rig(self, rig_root=None):
        """
        Perform comprehensive rig validation.
        
        Args:
            rig_root (str): Root group of the rig (optional)
            
        Returns:
            dict: Validation results
        """
        try:
            om.MGlobal.displayInfo("Starting rig validation...")
            
            # Clear previous results
            self.validation_results.clear()
            
            # Run validation checks
            self.validation_results["naming"] = self._validate_naming(rig_root)
            self.validation_results["hierarchy"] = self._validate_hierarchy(rig_root)
            self.validation_results["joints"] = self._validate_joints(rig_root)
            self.validation_results["controls"] = self._validate_controls(rig_root)
            self.validation_results["constraints"] = self._validate_constraints(rig_root)
            self.validation_results["skinning"] = self._validate_skinning(rig_root)
            self.validation_results["transforms"] = self._validate_transforms(rig_root)
            
            # Calculate overall score
            self.validation_results["overall_score"] = self._calculate_overall_score()
            
            om.MGlobal.displayInfo("Rig validation completed")
            return self.validation_results
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to validate rig: {str(e)}")
            raise
    
    def _validate_naming(self, rig_root):
        """Validate naming conventions."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Get all objects in rig
            objects = self._get_rig_objects(rig_root)
            
            for obj in objects:
                # Check if name follows convention
                if not self.naming.is_valid_name(obj):
                    results["issues"].append(f"Invalid naming: {obj}")
                    results["score"] -= 5
                
                # Check for illegal characters
                if any(char in obj for char in [" ", "-", ".", ":"]):
                    results["issues"].append(f"Illegal characters in name: {obj}")
                    results["score"] -= 3
                
                # Check for duplicate names
                if "|" in obj:
                    results["warnings"].append(f"Duplicate name detected: {obj}")
                    results["score"] -= 2
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Naming validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _validate_hierarchy(self, rig_root):
        """Validate rig hierarchy structure."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            if rig_root and cmds.objExists(rig_root):
                # Check for proper group structure
                children = cmds.listRelatives(rig_root, children=True) or []
                
                expected_groups = ["skeleton", "controls", "geometry"]
                found_groups = []
                
                for child in children:
                    for expected in expected_groups:
                        if expected in child.lower():
                            found_groups.append(expected)
                            break
                
                missing_groups = set(expected_groups) - set(found_groups)
                for missing in missing_groups:
                    results["warnings"].append(f"Missing expected group: {missing}")
                    results["score"] -= 10
            
            # Check for objects at world level that should be grouped
            world_objects = cmds.ls(assemblies=True)
            rig_objects = [obj for obj in world_objects if self.character_name in obj]
            
            if len(rig_objects) > 1:
                results["warnings"].append("Multiple rig objects at world level")
                results["score"] -= 15
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Hierarchy validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _validate_joints(self, rig_root):
        """Validate joint setup."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Get all joints
            joints = cmds.ls(type="joint")
            rig_joints = [j for j in joints if self.character_name in j]
            
            for joint in rig_joints:
                # Check joint orientation
                rotation = cmds.xform(joint, query=True, rotation=True, objectSpace=True)
                if any(abs(r) > 0.001 for r in rotation):
                    results["warnings"].append(f"Joint has non-zero rotation: {joint}")
                    results["score"] -= 2
                
                # Check for frozen transforms
                if cmds.getAttr(f"{joint}.jointOrientX") == 0 and \
                   cmds.getAttr(f"{joint}.jointOrientY") == 0 and \
                   cmds.getAttr(f"{joint}.jointOrientZ") == 0:
                    children = cmds.listRelatives(joint, children=True, type="joint")
                    if children:
                        results["warnings"].append(f"Joint may need orientation: {joint}")
                        results["score"] -= 1
                
                # Check for locked attributes
                locked_attrs = []
                for attr in ["tx", "ty", "tz", "rx", "ry", "rz", "sx", "sy", "sz"]:
                    if cmds.getAttr(f"{joint}.{attr}", lock=True):
                        locked_attrs.append(attr)
                
                if locked_attrs:
                    results["warnings"].append(f"Joint has locked attributes: {joint} ({locked_attrs})")
                    results["score"] -= 1
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Joint validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _validate_controls(self, rig_root):
        """Validate control setup."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Get all curves (controls)
            curves = cmds.ls(type="nurbsCurve")
            control_transforms = []
            
            for curve in curves:
                transform = cmds.listRelatives(curve, parent=True)[0]
                if self.character_name in transform and "ctrl" in transform:
                    control_transforms.append(transform)
            
            for control in control_transforms:
                # Check for proper color override
                shapes = cmds.listRelatives(control, shapes=True)
                if shapes:
                    for shape in shapes:
                        if not cmds.getAttr(f"{shape}.overrideEnabled"):
                            results["warnings"].append(f"Control has no color override: {control}")
                            results["score"] -= 2
                
                # Check for frozen transforms
                for attr in ["tx", "ty", "tz", "rx", "ry", "rz", "sx", "sy", "sz"]:
                    value = cmds.getAttr(f"{control}.{attr}")
                    if abs(value) > 0.001:
                        results["warnings"].append(f"Control has non-zero {attr}: {control}")
                        results["score"] -= 1
                
                # Check for keyable attributes
                keyable_attrs = cmds.listAttr(control, keyable=True)
                if not keyable_attrs or len(keyable_attrs) < 3:
                    results["warnings"].append(f"Control has few keyable attributes: {control}")
                    results["score"] -= 1
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Control validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _validate_constraints(self, rig_root):
        """Validate constraint setup."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Get all constraints
            constraint_types = ["parentConstraint", "pointConstraint", "orientConstraint", 
                              "scaleConstraint", "aimConstraint", "poleVectorConstraint"]
            
            for constraint_type in constraint_types:
                constraints = cmds.ls(type=constraint_type)
                rig_constraints = [c for c in constraints if self.character_name in c]
                
                for constraint in rig_constraints:
                    # Check if constraint is connected
                    connections = cmds.listConnections(constraint, source=False, destination=True)
                    if not connections:
                        results["issues"].append(f"Constraint not connected: {constraint}")
                        results["score"] -= 5
                    
                    # Check constraint weights
                    if constraint_type in ["parentConstraint", "pointConstraint", "orientConstraint"]:
                        try:
                            weight_attrs = cmds.listAttr(constraint, string="*W*")
                            if weight_attrs:
                                total_weight = sum(cmds.getAttr(f"{constraint}.{attr}") for attr in weight_attrs)
                                if abs(total_weight - 1.0) > 0.001:
                                    results["warnings"].append(f"Constraint weights don't sum to 1.0: {constraint}")
                                    results["score"] -= 2
                        except:
                            pass
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Constraint validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _validate_skinning(self, rig_root):
        """Validate skinning setup."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Get all skin clusters
            skin_clusters = cmds.ls(type="skinCluster")
            rig_skin_clusters = [sc for sc in skin_clusters if self.character_name in sc]
            
            for skin_cluster in rig_skin_clusters:
                # Check for unused influences
                influences = cmds.skinCluster(skin_cluster, query=True, influence=True)
                geometry = cmds.skinCluster(skin_cluster, query=True, geometry=True)
                
                if geometry:
                    mesh = geometry[0]
                    vertex_count = cmds.polyEvaluate(mesh, vertex=True)
                    
                    # Check for influences with zero weights
                    unused_influences = []
                    for influence in influences:
                        max_weight = 0
                        for i in range(vertex_count):
                            weights = cmds.skinPercent(skin_cluster, f"{mesh}.vtx[{i}]", 
                                                     query=True, value=True, transform=influence)
                            if weights and weights[0] > max_weight:
                                max_weight = weights[0]
                        
                        if max_weight < 0.001:
                            unused_influences.append(influence)
                    
                    if unused_influences:
                        results["warnings"].append(f"Unused influences in {skin_cluster}: {unused_influences}")
                        results["score"] -= len(unused_influences)
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Skinning validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _validate_transforms(self, rig_root):
        """Validate transform values."""
        results = {
            "score": 100,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Get all transforms
            objects = self._get_rig_objects(rig_root)
            
            for obj in objects:
                if cmds.objectType(obj) == "transform":
                    # Check for extreme scale values
                    scale = cmds.xform(obj, query=True, scale=True, relative=True)
                    if any(s < 0.001 or s > 1000 for s in scale):
                        results["warnings"].append(f"Extreme scale values: {obj}")
                        results["score"] -= 3
                    
                    # Check for non-uniform scale on joints
                    if cmds.objectType(obj) == "joint":
                        if not all(abs(s - scale[0]) < 0.001 for s in scale):
                            results["warnings"].append(f"Non-uniform scale on joint: {obj}")
                            results["score"] -= 2
            
            results["score"] = max(0, results["score"])
            
        except Exception as e:
            results["issues"].append(f"Transform validation error: {str(e)}")
            results["score"] = 0
        
        return results
    
    def _get_rig_objects(self, rig_root):
        """Get all objects belonging to the rig."""
        if rig_root and cmds.objExists(rig_root):
            return cmds.listRelatives(rig_root, allDescendents=True, fullPath=True) or []
        else:
            # Get all objects with character name
            all_objects = cmds.ls()
            return [obj for obj in all_objects if self.character_name in obj]
    
    def _calculate_overall_score(self):
        """Calculate overall validation score."""
        if not self.validation_results:
            return 0
        
        scores = []
        for category, result in self.validation_results.items():
            if isinstance(result, dict) and "score" in result:
                scores.append(result["score"])
        
        return sum(scores) / len(scores) if scores else 0
    
    def get_validation_report(self):
        """
        Get a formatted validation report.
        
        Returns:
            str: Formatted validation report
        """
        if not self.validation_results:
            return "No validation results available. Run validate_rig() first."
        
        report = f"RIG VALIDATION REPORT - {self.character_name}\n"
        report += "=" * 50 + "\n\n"
        
        overall_score = self.validation_results.get("overall_score", 0)
        report += f"Overall Score: {overall_score:.1f}/100\n\n"
        
        for category, result in self.validation_results.items():
            if category == "overall_score":
                continue
                
            if isinstance(result, dict):
                score = result.get("score", 0)
                issues = result.get("issues", [])
                warnings = result.get("warnings", [])
                
                report += f"{category.upper()}: {score}/100\n"
                
                if issues:
                    report += "  Issues:\n"
                    for issue in issues:
                        report += f"    - {issue}\n"
                
                if warnings:
                    report += "  Warnings:\n"
                    for warning in warnings:
                        report += f"    - {warning}\n"
                
                report += "\n"
        
        return report
