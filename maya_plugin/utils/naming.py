"""
命名规范模块

此模块为角色绑定提供标准化的命名规范。
"""

import re


class NamingConvention:
    """
    角色绑定组件的标准化命名规范。

    命名模式: [角色名]_[组件]_[侧面]_[类型]
    示例:
    - character_spine_01_jnt
    - character_arm_L_ctrl
    - character_leg_R_ik_hdl
    """
    
    def __init__(self, character_name="character"):
        """
        初始化命名规范。

        Args:
            character_name (str): 基础角色名称
        """
        self.character_name = self._clean_name(character_name)

        # 命名后缀
        self.suffixes = {
            "joint": "jnt",
            "control": "ctrl",
            "group": "grp",
            "locator": "loc",
            "constraint": "con",
            "handle": "hdl",
            "curve": "crv",
            "surface": "srf",
            "mesh": "geo",
            "material": "mat",
            "texture": "tex",
            "deformer": "def",
            "cluster": "cls",
            "blendshape": "bs",
            "skincluster": "sc",
            "ikhandle": "ik",
            "effector": "eff",
            "pole_vector": "pv"
        }

        # 侧面指示器
        self.sides = {
            "left": "L",
            "right": "R",
            "center": "C",
            "middle": "M"
        }
        
        # 常用组件名称
        self.components = {
            # 脊椎
            "root": "root",
            "spine": "spine",
            "chest": "chest",
            "neck": "neck",
            "head": "head",

            # 手臂
            "clavicle": "clavicle",
            "shoulder": "shoulder",
            "upperarm": "upperarm",
            "elbow": "elbow",
            "lowerarm": "lowerarm",
            "wrist": "wrist",
            "hand": "hand",

            # 手指
            "thumb": "thumb",
            "index": "index",
            "middle": "middle",
            "ring": "ring",
            "pinky": "pinky",

            # 腿部
            "hip": "hip",
            "upperleg": "upperleg",
            "knee": "knee",
            "lowerleg": "lowerleg",
            "ankle": "ankle",
            "foot": "foot",
            "toe": "toe",

            # 面部
            "eye": "eye",
            "eyebrow": "eyebrow",
            "eyelid": "eyelid",
            "cheek": "cheek",
            "mouth": "mouth",
            "lip": "lip",
            "jaw": "jaw"
        }
    
    def _clean_name(self, name):
        """
        清理名称使其在Maya中有效。

        Args:
            name (str): 输入名称

        Returns:
            str: 清理后的名称
        """
        # 移除无效字符并用下划线替换
        cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', name)

        # 确保以字母开头
        if cleaned and not cleaned[0].isalpha():
            cleaned = 'n_' + cleaned

        # 移除多个下划线
        cleaned = re.sub(r'_+', '_', cleaned)

        # 移除尾部下划线
        cleaned = cleaned.strip('_')

        return cleaned or "unnamed"
    
    def get_joint_name(self, component, side=None, index=None):
        """
        获取标准化的关节名称。

        Args:
            component (str): 组件名称（例如："spine", "upperarm"）
            side (str): 侧面指示器（"left", "right", "center"）
            index (int): 编号组件的索引号

        Returns:
            str: 格式化的关节名称
        """
        parts = [self.character_name]
        
        # Add component
        component_clean = self.components.get(component, self._clean_name(component))
        parts.append(component_clean)
        
        # Add index if provided
        if index is not None:
            parts.append(f"{index:02d}")
        
        # Add side if provided
        if side:
            side_code = self.sides.get(side.lower(), side.upper())
            parts.append(side_code)
        
        # Add suffix
        parts.append(self.suffixes["joint"])
        
        return "_".join(parts)
    
    def get_control_name(self, component, side=None, index=None, control_type=None):
        """
        获取标准化的控制器名称。

        Args:
            component (str): 组件名称
            side (str): 侧面指示器
            index (int): 索引号
            control_type (str): 控制器类型（例如："ik", "fk", "main"）

        Returns:
            str: 格式化的控制器名称
        """
        parts = [self.character_name]
        
        # Add component
        component_clean = self.components.get(component, self._clean_name(component))
        parts.append(component_clean)
        
        # Add index if provided
        if index is not None:
            parts.append(f"{index:02d}")
        
        # Add side if provided
        if side:
            side_code = self.sides.get(side.lower(), side.upper())
            parts.append(side_code)
        
        # Add control type if provided
        if control_type:
            parts.append(self._clean_name(control_type))
        
        # Add suffix
        parts.append(self.suffixes["control"])
        
        return "_".join(parts)
    
    def get_group_name(self, component, side=None, index=None, group_type=None):
        """
        获取标准化的组名称。

        Args:
            component (str): 组件名称
            side (str): 侧面指示器
            index (int): 索引号
            group_type (str): 组类型（例如："offset", "sdk", "const"）

        Returns:
            str: 格式化的组名称
        """
        parts = [self.character_name]
        
        # Add component
        component_clean = self.components.get(component, self._clean_name(component))
        parts.append(component_clean)
        
        # Add index if provided
        if index is not None:
            parts.append(f"{index:02d}")
        
        # Add side if provided
        if side:
            side_code = self.sides.get(side.lower(), side.upper())
            parts.append(side_code)
        
        # Add group type if provided
        if group_type:
            parts.append(self._clean_name(group_type))
        
        # Add suffix
        parts.append(self.suffixes["group"])
        
        return "_".join(parts)
    
    def get_locator_name(self, component, side=None, index=None, purpose=None):
        """
        获取标准化的定位器名称。

        Args:
            component (str): 组件名称
            side (str): 侧面指示器
            index (int): 索引号
            purpose (str): 定位器用途（例如："aim", "up", "target"）

        Returns:
            str: 格式化的定位器名称
        """
        parts = [self.character_name]
        
        # Add component
        component_clean = self.components.get(component, self._clean_name(component))
        parts.append(component_clean)
        
        # Add index if provided
        if index is not None:
            parts.append(f"{index:02d}")
        
        # Add side if provided
        if side:
            side_code = self.sides.get(side.lower(), side.upper())
            parts.append(side_code)
        
        # Add purpose if provided
        if purpose:
            parts.append(self._clean_name(purpose))
        
        # Add suffix
        parts.append(self.suffixes["locator"])
        
        return "_".join(parts)
    
    def get_constraint_name(self, target, constraint_type):
        """
        获取标准化的约束名称。

        Args:
            target (str): 目标对象名称
            constraint_type (str): 约束类型（例如："parent", "point", "orient"）

        Returns:
            str: 格式化的约束名称
        """
        # Extract base name from target
        base_name = target.split("_")[0] if "_" in target else target
        
        parts = [base_name, self._clean_name(constraint_type), self.suffixes["constraint"]]
        return "_".join(parts)
    
    def get_ik_handle_name(self, component, side=None):
        """
        获取标准化的IK手柄名称。

        Args:
            component (str): 组件名称
            side (str): 侧面指示器

        Returns:
            str: 格式化的IK手柄名称
        """
        parts = [self.character_name]
        
        # Add component
        component_clean = self.components.get(component, self._clean_name(component))
        parts.append(component_clean)
        
        # Add side if provided
        if side:
            side_code = self.sides.get(side.lower(), side.upper())
            parts.append(side_code)
        
        # Add suffix
        parts.append(self.suffixes["ikhandle"])
        
        return "_".join(parts)
    
    def parse_name(self, name):
        """
        解析名称以提取其组件。

        Args:
            name (str): 要解析的名称

        Returns:
            dict: 包含解析组件的字典
        """
        parts = name.split("_")
        
        result = {
            "character": None,
            "component": None,
            "index": None,
            "side": None,
            "type": None,
            "suffix": None
        }
        
        if len(parts) >= 1:
            result["character"] = parts[0]
        
        if len(parts) >= 2:
            result["component"] = parts[1]
        
        # Check for suffix
        if len(parts) >= 1:
            last_part = parts[-1]
            for suffix_type, suffix in self.suffixes.items():
                if last_part == suffix:
                    result["suffix"] = suffix
                    result["type"] = suffix_type
                    break
        
        # Check for side indicators
        for part in parts:
            if part in self.sides.values():
                result["side"] = part
                break
        
        # Check for index (numeric parts)
        for part in parts:
            if part.isdigit():
                result["index"] = int(part)
                break
        
        return result
    
    def is_valid_name(self, name):
        """
        检查名称是否遵循命名规范。

        Args:
            name (str): 要验证的名称

        Returns:
            bool: 如果名称有效则返回True
        """
        # Basic validation
        if not name or not isinstance(name, str):
            return False
        
        # Check for invalid characters
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', name):
            return False
        
        # Check if it has a recognized suffix
        parts = name.split("_")
        if len(parts) < 2:
            return False
        
        last_part = parts[-1]
        return last_part in self.suffixes.values()
