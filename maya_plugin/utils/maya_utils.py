"""
Maya实用函数

此模块提供常见Maya操作的实用函数。
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om


def get_maya_version():
    """
    获取当前Maya版本。

    Returns:
        str: Maya版本字符串（例如："2024"）
    """
    return cmds.about(version=True)


def get_selected_objects():
    """
    获取Maya中当前选中的对象。

    Returns:
        list: 选中对象名称列表
    """
    selection = cmds.ls(selection=True)
    return selection if selection else []


def create_locator_at_position(position=(0, 0, 0), name="locator"):
    """
    在指定位置创建定位器。

    Args:
        position (tuple): 世界位置 (x, y, z)
        name (str): 定位器名称

    Returns:
        str: 创建的定位器名称
    """
    locator = cmds.spaceLocator(name=name)[0]
    cmds.xform(locator, worldSpace=True, translation=position)
    return locator


def safe_delete_objects(objects):
    """
    安全删除对象，首先检查它们是否存在。

    Args:
        objects (list or str): 要删除的对象名称
    """
    if isinstance(objects, str):
        objects = [objects]

    for obj in objects:
        if cmds.objExists(obj):
            try:
                cmds.delete(obj)
            except Exception as e:
                om.MGlobal.displayWarning(f"无法删除 {obj}: {str(e)}")


def get_object_world_position(obj):
    """
    获取对象的世界位置。

    Args:
        obj (str): 对象名称

    Returns:
        tuple: 世界位置 (x, y, z)，如果对象不存在则返回None
    """
    if not cmds.objExists(obj):
        return None

    try:
        position = cmds.xform(obj, query=True, worldSpace=True, translation=True)
        return tuple(position)
    except Exception as e:
        om.MGlobal.displayWarning(f"无法获取 {obj} 的位置: {str(e)}")
        return None


def set_object_world_position(obj, position):
    """
    设置对象的世界位置。

    Args:
        obj (str): 对象名称
        position (tuple): 世界位置 (x, y, z)

    Returns:
        bool: 成功返回True，否则返回False
    """
    if not cmds.objExists(obj):
        return False

    try:
        cmds.xform(obj, worldSpace=True, translation=position)
        return True
    except Exception as e:
        om.MGlobal.displayWarning(f"无法设置 {obj} 的位置: {str(e)}")
        return False


def get_object_bounding_box(obj):
    """
    获取对象的边界框。

    Args:
        obj (str): 对象名称

    Returns:
        tuple: 边界框 (xmin, ymin, zmin, xmax, ymax, zmax)，如果失败则返回None
    """
    if not cmds.objExists(obj):
        return None

    try:
        bbox = cmds.exactWorldBoundingBox(obj)
        return tuple(bbox)
    except Exception as e:
        om.MGlobal.displayWarning(f"无法获取 {obj} 的边界框: {str(e)}")
        return None


def create_group(objects=None, name="group"):
    """
    创建组并可选择将对象作为其子级。

    Args:
        objects (list): 要分组的对象列表（可选）
        name (str): 组的名称

    Returns:
        str: 创建的组名称
    """
    if objects is None:
        objects = get_selected_objects()

    # 创建空组
    group = cmds.group(empty=True, name=name)

    # 如果提供了对象，则将其作为组的子级
    if objects:
        for obj in objects:
            if cmds.objExists(obj):
                try:
                    cmds.parent(obj, group)
                except Exception as e:
                    om.MGlobal.displayWarning(f"无法将 {obj} 作为 {group} 的子级: {str(e)}")

    return group


def duplicate_object(obj, name=None):
    """
    复制对象。

    Args:
        obj (str): 要复制的对象
        name (str): 复制对象的名称（可选）

    Returns:
        str: 复制对象的名称，如果失败则返回None
    """
    if not cmds.objExists(obj):
        return None

    try:
        duplicate = cmds.duplicate(obj, returnRootsOnly=True)[0]
        if name:
            duplicate = cmds.rename(duplicate, name)
        return duplicate
    except Exception as e:
        om.MGlobal.displayWarning(f"无法复制 {obj}: {str(e)}")
        return None


def get_object_type(obj):
    """
    Get the type of an object.
    
    Args:
        obj (str): Object name
    
    Returns:
        str: Object type or None if object doesn't exist
    """
    if not cmds.objExists(obj):
        return None
    
    try:
        return cmds.objectType(obj)
    except Exception as e:
        om.MGlobal.displayWarning(f"Could not get type for {obj}: {str(e)}")
        return None


def is_transform_node(obj):
    """
    Check if an object is a transform node.
    
    Args:
        obj (str): Object name
    
    Returns:
        bool: True if object is a transform node
    """
    obj_type = get_object_type(obj)
    return obj_type == "transform" if obj_type else False


def get_shape_nodes(transform):
    """
    Get shape nodes under a transform.
    
    Args:
        transform (str): Transform node name
    
    Returns:
        list: List of shape node names
    """
    if not cmds.objExists(transform):
        return []
    
    try:
        shapes = cmds.listRelatives(transform, shapes=True, fullPath=False)
        return shapes if shapes else []
    except Exception as e:
        om.MGlobal.displayWarning(f"Could not get shapes for {transform}: {str(e)}")
        return []


def freeze_transformations(objects=None):
    """
    Freeze transformations on objects.
    
    Args:
        objects (list): List of objects (uses selection if None)
    
    Returns:
        bool: True if successful
    """
    if objects is None:
        objects = get_selected_objects()
    
    if not objects:
        return False
    
    try:
        cmds.makeIdentity(objects, apply=True, translate=True, rotate=True, scale=True)
        return True
    except Exception as e:
        om.MGlobal.displayWarning(f"Could not freeze transformations: {str(e)}")
        return False
