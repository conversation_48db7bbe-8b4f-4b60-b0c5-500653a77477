"""
Character Rigging Main Window

This module provides the main user interface for the character rigging plugin.
"""

import maya.cmds as cmds
import maya.OpenMayaUI as omui

try:
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    from PySide2.QtWidgets import *
    from shiboken2 import wrapInstance
except ImportError:
    try:
        from PySide6.QtCore import *
        from PySide6.QtGui import *
        from PySide6.QtWidgets import *
        from shiboken6 import wrapInstance
    except ImportError:
        try:
            from PyQt5.QtCore import *
            from PyQt5.QtGui import *
            from PyQt5.QtWidgets import *
            from sip import wrapinstance as wrapInstance
        except ImportError:
            print("Warning: No Qt bindings found. UI functionality will be limited.")
            QWidget = object
            wrapInstance = None


def get_maya_main_window():
    """
    Get <PERSON>'s main window as a Qt widget.
    
    Returns:
        QWidget: Maya's main window
    """
    if wrapInstance is None:
        return None
        
    main_window_ptr = omui.MQtUtil.mainWindow()
    if main_window_ptr is not None:
        return wrapInstance(int(main_window_ptr), QWidget)
    return None


class MainWindow(QDialog):
    """
    Main character rigging window providing a user interface for rigging functionality.
    """

    WINDOW_TITLE = "Character Auto Rigging"
    WINDOW_NAME = "characterAutoRiggingWindow"
    
    def __init__(self, parent=None):
        """
        初始化主窗口。

        Args:
            parent: 父窗口部件（默认为Maya主窗口）
        """
        if parent is None:
            parent = get_maya_main_window()

        super(MainWindow, self).__init__(parent)

        # 设置窗口属性
        self.setWindowTitle(self.WINDOW_TITLE)
        self.setObjectName(self.WINDOW_NAME)
        self.setMinimumSize(300, 200)
        self.resize(400, 300)

        # 使窗口保持在Maya之上
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)

        # 初始化UI
        self.create_widgets()
        self.create_layouts()
        self.create_connections()
    
    def create_widgets(self):
        """创建所有UI部件。"""
        # 标题标签
        self.title_label = QLabel(self.WINDOW_TITLE)
        self.title_label.setAlignment(Qt.AlignCenter)
        font = self.title_label.font()
        font.setPointSize(14)
        font.setBold(True)
        self.title_label.setFont(font)

        # 描述
        self.description_label = QLabel(
            "这是角色自动绑定插件界面。\n"
            "使用下面的按钮测试插件功能。"
        )
        self.description_label.setAlignment(Qt.AlignCenter)
        self.description_label.setWordWrap(True)

        # 命令部分
        self.command_group = QGroupBox("命令")

        self.example_command_btn = QPushButton("运行自动绑定")
        self.example_command_btn.setToolTip("执行角色自动绑定命令")
        
        # 节点部分
        self.node_group = QGroupBox("节点")

        self.create_node_btn = QPushButton("创建示例节点")
        self.create_node_btn.setToolTip("创建示例节点实例")

        # 节点测试输入字段
        self.input_a_label = QLabel("输入A:")
        self.input_a_spinbox = QDoubleSpinBox()
        self.input_a_spinbox.setRange(-999999.0, 999999.0)
        self.input_a_spinbox.setValue(2.0)

        self.input_b_label = QLabel("输入B:")
        self.input_b_spinbox = QDoubleSpinBox()
        self.input_b_spinbox.setRange(-999999.0, 999999.0)
        self.input_b_spinbox.setValue(3.0)

        self.test_node_btn = QPushButton("测试节点计算")
        self.test_node_btn.setToolTip("使用输入值测试示例节点")

        # 结果
        self.results_group = QGroupBox("结果")
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(100)
        self.results_text.setReadOnly(True)

        # 控制按钮
        self.clear_btn = QPushButton("清除结果")
        self.close_btn = QPushButton("关闭")
    
    def create_layouts(self):
        """创建和设置布局。"""
        # 主布局
        main_layout = QVBoxLayout(self)

        # 头部
        main_layout.addWidget(self.title_label)
        main_layout.addWidget(self.description_label)
        main_layout.addSpacing(10)

        # 命令组布局
        command_layout = QVBoxLayout(self.command_group)
        command_layout.addWidget(self.example_command_btn)

        # 节点组布局
        node_layout = QVBoxLayout(self.node_group)
        node_layout.addWidget(self.create_node_btn)

        # 输入布局
        input_layout = QGridLayout()
        input_layout.addWidget(self.input_a_label, 0, 0)
        input_layout.addWidget(self.input_a_spinbox, 0, 1)
        input_layout.addWidget(self.input_b_label, 1, 0)
        input_layout.addWidget(self.input_b_spinbox, 1, 1)
        
        node_layout.addLayout(input_layout)
        node_layout.addWidget(self.test_node_btn)
        
        # 结果组布局
        results_layout = QVBoxLayout(self.results_group)
        results_layout.addWidget(self.results_text)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        # 添加到主布局
        main_layout.addWidget(self.command_group)
        main_layout.addWidget(self.node_group)
        main_layout.addWidget(self.results_group)
        main_layout.addLayout(button_layout)

    def create_connections(self):
        """连接信号和槽。"""
        self.example_command_btn.clicked.connect(self.run_example_command)
        self.create_node_btn.clicked.connect(self.create_example_node)
        self.test_node_btn.clicked.connect(self.test_node_calculation)
        self.clear_btn.clicked.connect(self.clear_results)
        self.close_btn.clicked.connect(self.close)
    
    def run_example_command(self):
        """执行自动绑定命令。"""
        try:
            result = cmds.autoRigCharacter(verbose=True)
            self.add_result(f"自动绑定命令执行成功。已创建: {result}")
        except Exception as e:
            self.add_result(f"运行自动绑定命令错误: {str(e)}")

    def create_example_node(self):
        """创建示例节点。"""
        try:
            node = cmds.createNode("exampleNode")
            self.add_result(f"示例节点已创建: {node}")
        except Exception as e:
            self.add_result(f"创建示例节点错误: {str(e)}")
    
    def test_node_calculation(self):
        """使用当前输入值测试节点计算。"""
        try:
            # 创建临时节点进行测试
            node = cmds.createNode("exampleNode")

            # 设置输入值
            input_a = self.input_a_spinbox.value()
            input_b = self.input_b_spinbox.value()

            cmds.setAttr(f"{node}.inputA", input_a)
            cmds.setAttr(f"{node}.inputB", input_b)

            # 获取结果
            result = cmds.getAttr(f"{node}.output")

            self.add_result(f"节点计算: {input_a} × {input_b} = {result}")

            # 清理
            cmds.delete(node)

        except Exception as e:
            self.add_result(f"测试节点错误: {str(e)}")
    
    def add_result(self, message):
        """
        向结果文本区域添加消息。

        Args:
            message (str): 要添加的消息
        """
        self.results_text.append(f"• {message}")
        # 滚动到底部
        scrollbar = self.results_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_results(self):
        """清除结果文本区域。"""
        self.results_text.clear()
    
    @classmethod
    def show_window(cls):
        """
        显示主窗口。如果已存在，则将其置于前台。

        Returns:
            MainWindow: 窗口实例
        """
        # 如果存在现有窗口则关闭
        if cmds.window(cls.WINDOW_NAME, exists=True):
            cmds.deleteUI(cls.WINDOW_NAME, window=True)

        # 创建并显示新窗口
        window = cls()
        window.show()
        return window


# 便捷函数，便于访问
def show_main_window():
    """显示主插件窗口。"""
    return MainWindow.show_window()

def show_rigging_window():
    """显示角色绑定窗口。"""
    return MainWindow.show_window()
