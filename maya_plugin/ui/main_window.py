"""
Character Rigging Main Window

This module provides the main user interface for the character rigging plugin.
"""

import maya.cmds as cmds
import maya.OpenMayaUI as omui

try:
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    from PySide2.QtWidgets import *
    from shiboken2 import wrapInstance
except ImportError:
    try:
        from PySide6.QtCore import *
        from PySide6.QtGui import *
        from PySide6.QtWidgets import *
        from shiboken6 import wrapInstance
    except ImportError:
        try:
            from PyQt5.QtCore import *
            from PyQt5.QtGui import *
            from PyQt5.QtWidgets import *
            from sip import wrapinstance as wrapInstance
        except ImportError:
            print("Warning: No Qt bindings found. UI functionality will be limited.")
            QWidget = object
            wrapInstance = None


def get_maya_main_window():
    """
    Get <PERSON>'s main window as a Qt widget.
    
    Returns:
        QWidget: Maya's main window
    """
    if wrapInstance is None:
        return None
        
    main_window_ptr = omui.MQtUtil.mainWindow()
    if main_window_ptr is not None:
        return wrapInstance(int(main_window_ptr), QWidget)
    return None


class MainWindow(QDialog):
    """
    Main character rigging window providing a user interface for rigging functionality.
    """

    WINDOW_TITLE = "Character Auto Rigging"
    WINDOW_NAME = "characterAutoRiggingWindow"
    
    def __init__(self, parent=None):
        """
        Initialize the main window.
        
        Args:
            parent: Parent widget (defaults to Maya's main window)
        """
        if parent is None:
            parent = get_maya_main_window()
            
        super(MainWindow, self).__init__(parent)
        
        # Set window properties
        self.setWindowTitle(self.WINDOW_TITLE)
        self.setObjectName(self.WINDOW_NAME)
        self.setMinimumSize(300, 200)
        self.resize(400, 300)
        
        # Make window stay on top of Maya
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        
        # Initialize UI
        self.create_widgets()
        self.create_layouts()
        self.create_connections()
    
    def create_widgets(self):
        """Create all UI widgets."""
        # Title label
        self.title_label = QLabel(self.WINDOW_TITLE)
        self.title_label.setAlignment(Qt.AlignCenter)
        font = self.title_label.font()
        font.setPointSize(14)
        font.setBold(True)
        self.title_label.setFont(font)
        
        # Description
        self.description_label = QLabel(
            "This is an example Maya plugin interface.\n"
            "Use the buttons below to test plugin functionality."
        )
        self.description_label.setAlignment(Qt.AlignCenter)
        self.description_label.setWordWrap(True)
        
        # Command section
        self.command_group = QGroupBox("Commands")
        
        self.example_command_btn = QPushButton("Run Example Command")
        self.example_command_btn.setToolTip("Execute the example command to create a cube")
        
        # Node section
        self.node_group = QGroupBox("Nodes")
        
        self.create_node_btn = QPushButton("Create Example Node")
        self.create_node_btn.setToolTip("Create an instance of the example node")
        
        # Input fields for node testing
        self.input_a_label = QLabel("Input A:")
        self.input_a_spinbox = QDoubleSpinBox()
        self.input_a_spinbox.setRange(-999999.0, 999999.0)
        self.input_a_spinbox.setValue(2.0)
        
        self.input_b_label = QLabel("Input B:")
        self.input_b_spinbox = QDoubleSpinBox()
        self.input_b_spinbox.setRange(-999999.0, 999999.0)
        self.input_b_spinbox.setValue(3.0)
        
        self.test_node_btn = QPushButton("Test Node Calculation")
        self.test_node_btn.setToolTip("Test the example node with the input values")
        
        # Results
        self.results_group = QGroupBox("Results")
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(100)
        self.results_text.setReadOnly(True)
        
        # Control buttons
        self.clear_btn = QPushButton("Clear Results")
        self.close_btn = QPushButton("Close")
    
    def create_layouts(self):
        """Create and set up layouts."""
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Header
        main_layout.addWidget(self.title_label)
        main_layout.addWidget(self.description_label)
        main_layout.addSpacing(10)
        
        # Command group layout
        command_layout = QVBoxLayout(self.command_group)
        command_layout.addWidget(self.example_command_btn)
        
        # Node group layout
        node_layout = QVBoxLayout(self.node_group)
        node_layout.addWidget(self.create_node_btn)
        
        # Input layout
        input_layout = QGridLayout()
        input_layout.addWidget(self.input_a_label, 0, 0)
        input_layout.addWidget(self.input_a_spinbox, 0, 1)
        input_layout.addWidget(self.input_b_label, 1, 0)
        input_layout.addWidget(self.input_b_spinbox, 1, 1)
        
        node_layout.addLayout(input_layout)
        node_layout.addWidget(self.test_node_btn)
        
        # Results group layout
        results_layout = QVBoxLayout(self.results_group)
        results_layout.addWidget(self.results_text)
        
        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        # Add to main layout
        main_layout.addWidget(self.command_group)
        main_layout.addWidget(self.node_group)
        main_layout.addWidget(self.results_group)
        main_layout.addLayout(button_layout)
    
    def create_connections(self):
        """Connect signals and slots."""
        self.example_command_btn.clicked.connect(self.run_example_command)
        self.create_node_btn.clicked.connect(self.create_example_node)
        self.test_node_btn.clicked.connect(self.test_node_calculation)
        self.clear_btn.clicked.connect(self.clear_results)
        self.close_btn.clicked.connect(self.close)
    
    def run_example_command(self):
        """Execute the example command."""
        try:
            result = cmds.exampleCommand(verbose=True)
            self.add_result(f"Example command executed successfully. Created: {result}")
        except Exception as e:
            self.add_result(f"Error running example command: {str(e)}")
    
    def create_example_node(self):
        """Create an example node."""
        try:
            node = cmds.createNode("exampleNode")
            self.add_result(f"Example node created: {node}")
        except Exception as e:
            self.add_result(f"Error creating example node: {str(e)}")
    
    def test_node_calculation(self):
        """Test the node calculation with current input values."""
        try:
            # Create a temporary node for testing
            node = cmds.createNode("exampleNode")
            
            # Set input values
            input_a = self.input_a_spinbox.value()
            input_b = self.input_b_spinbox.value()
            
            cmds.setAttr(f"{node}.inputA", input_a)
            cmds.setAttr(f"{node}.inputB", input_b)
            
            # Get the result
            result = cmds.getAttr(f"{node}.output")
            
            self.add_result(f"Node calculation: {input_a} × {input_b} = {result}")
            
            # Clean up
            cmds.delete(node)
            
        except Exception as e:
            self.add_result(f"Error testing node: {str(e)}")
    
    def add_result(self, message):
        """
        Add a message to the results text area.
        
        Args:
            message (str): Message to add
        """
        self.results_text.append(f"• {message}")
        # Scroll to bottom
        scrollbar = self.results_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_results(self):
        """Clear the results text area."""
        self.results_text.clear()
    
    @classmethod
    def show_window(cls):
        """
        Show the main window. If it already exists, bring it to front.
        
        Returns:
            MainWindow: The window instance
        """
        # Close existing window if it exists
        if cmds.window(cls.WINDOW_NAME, exists=True):
            cmds.deleteUI(cls.WINDOW_NAME, window=True)
        
        # Create and show new window
        window = cls()
        window.show()
        return window


# Convenience functions for easy access
def show_main_window():
    """Show the main plugin window."""
    return MainWindow.show_window()

def show_rigging_window():
    """Show the character rigging window."""
    return MainWindow.show_window()
