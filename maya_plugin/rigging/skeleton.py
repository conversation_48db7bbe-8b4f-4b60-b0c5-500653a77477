"""
骨骼构建器模块

此模块提供创建和管理角色骨骼的工具。
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om
from ..utils.naming import NamingConvention
from ..utils.maya_utils import get_object_world_position, set_object_world_position


class SkeletonBuilder:
    """
    构建具有正确命名和层次结构的角色骨骼的主要类。
    """

    def __init__(self, character_name="character", skeleton_type="biped"):
        """
        初始化骨骼构建器。

        Args:
            character_name (str): 角色的名称前缀
            skeleton_type (str): 骨骼类型（双足、四足等）
        """
        self.character_name = character_name
        self.skeleton_type = skeleton_type
        self.naming = NamingConvention(character_name)
        self.joints = {}
        self.joint_hierarchy = []

        # 定义骨骼模板
        self.skeleton_templates = {
            "biped": self._get_biped_template(),
            "quadruped": self._get_quadruped_template()
        }

    def _get_biped_template(self):
        """
        获取包含关节位置和层次结构的双足骨骼模板。

        Returns:
            dict: 骨骼模板数据
        """
        return {
            "root": {
                "position": (0, 0, 0),
                "children": ["spine_01"]
            },
            "spine_01": {
                "position": (0, 10, 0),
                "children": ["spine_02"]
            },
            "spine_02": {
                "position": (0, 12, 0),
                "children": ["spine_03"]
            },
            "spine_03": {
                "position": (0, 14, 0),
                "children": ["neck_01", "clavicle_L", "clavicle_R"]
            },
            "neck_01": {
                "position": (0, 16, 0),
                "children": ["head"]
            },
            "head": {
                "position": (0, 18, 0),
                "children": []
            },
            "clavicle_L": {
                "position": (1, 15, 0),
                "children": ["upperarm_L"]
            },
            "clavicle_R": {
                "position": (-1, 15, 0),
                "children": ["upperarm_R"]
            },
            "upperarm_L": {
                "position": (3, 15, 0),
                "children": ["lowerarm_L"]
            },
            "upperarm_R": {
                "position": (-3, 15, 0),
                "children": ["lowerarm_R"]
            },
            "lowerarm_L": {
                "position": (6, 15, 0),
                "children": ["hand_L"]
            },
            "lowerarm_R": {
                "position": (-6, 15, 0),
                "children": ["hand_R"]
            },
            "hand_L": {
                "position": (9, 15, 0),
                "children": []
            },
            "hand_R": {
                "position": (-9, 15, 0),
                "children": []
            }
        }
    
    def _get_quadruped_template(self):
        """
        获取四足骨骼模板。

        Returns:
            dict: 骨骼模板数据
        """
        # 简化的四足模板
        return {
            "root": {
                "position": (0, 8, 0),
                "children": ["spine_01"]
            },
            "spine_01": {
                "position": (0, 8, -2),
                "children": ["spine_02"]
            },
            "spine_02": {
                "position": (0, 8, -4),
                "children": ["neck_01"]
            },
            "neck_01": {
                "position": (0, 9, -6),
                "children": ["head"]
            },
            "head": {
                "position": (0, 10, -8),
                "children": []
            }
        }
    
    def create_skeleton(self, scale=1.0):
        """
        基于模板创建完整的骨骼。

        Args:
            scale (float): 骨骼的缩放因子

        Returns:
            list: 创建的关节名称列表
        """
        try:
            template = self.skeleton_templates.get(self.skeleton_type)
            if not template:
                raise ValueError(f"Unknown skeleton type: {self.skeleton_type}")
            
            # Clear previous joints
            self.joints.clear()
            
            # Create joints recursively
            root_joint = self._create_joint_hierarchy("root", template, scale)
            
            # Orient joints properly
            self._orient_joints()
            
            # Create joint hierarchy list
            self.joint_hierarchy = list(self.joints.keys())
            
            om.MGlobal.displayInfo(f"Successfully created {self.skeleton_type} skeleton for {self.character_name}")
            return self.joint_hierarchy
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create skeleton: {str(e)}")
            raise
    
    def _create_joint_hierarchy(self, joint_name, template, scale, parent=None):
        """
        Recursively create joint hierarchy.
        
        Args:
            joint_name (str): Name of the joint to create
            template (dict): Skeleton template
            scale (float): Scale factor
            parent (str): Parent joint name
            
        Returns:
            str: Created joint name
        """
        if joint_name not in template:
            return None
        
        joint_data = template[joint_name]
        position = [p * scale for p in joint_data["position"]]
        
        # Create joint
        if parent:
            cmds.select(parent)
        else:
            cmds.select(clear=True)
        
        joint = cmds.joint(name=self.naming.get_joint_name(joint_name))
        cmds.xform(joint, worldSpace=True, translation=position)
        
        # Store joint reference
        self.joints[joint_name] = joint
        
        # Create children
        for child_name in joint_data.get("children", []):
            self._create_joint_hierarchy(child_name, template, scale, joint)
        
        return joint
    
    def _orient_joints(self):
        """Orient all joints properly for rigging."""
        try:
            # Orient joints to point down the bone chain
            for joint_name, joint in self.joints.items():
                if cmds.objExists(joint):
                    # Get children
                    children = cmds.listRelatives(joint, children=True, type="joint")
                    if children:
                        # Orient towards first child
                        cmds.joint(joint, edit=True, orientJoint="xyz", secondaryAxisOrient="yup")
                    else:
                        # End joint - match parent orientation
                        parent = cmds.listRelatives(joint, parent=True, type="joint")
                        if parent:
                            parent_orient = cmds.xform(parent[0], query=True, rotation=True, worldSpace=True)
                            cmds.xform(joint, rotation=parent_orient, worldSpace=True)
                            
        except Exception as e:
            om.MGlobal.displayWarning(f"Joint orientation warning: {str(e)}")
    
    def add_custom_joint(self, joint_name, position, parent=None):
        """
        Add a custom joint to the skeleton.
        
        Args:
            joint_name (str): Name of the joint
            position (tuple): World position (x, y, z)
            parent (str): Parent joint name (optional)
            
        Returns:
            str: Created joint name
        """
        try:
            if parent and parent in self.joints:
                cmds.select(self.joints[parent])
            else:
                cmds.select(clear=True)
            
            joint = cmds.joint(name=self.naming.get_joint_name(joint_name))
            cmds.xform(joint, worldSpace=True, translation=position)
            
            self.joints[joint_name] = joint
            return joint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to add custom joint: {str(e)}")
            raise
    
    def get_joint(self, joint_name):
        """
        Get a joint by its logical name.
        
        Args:
            joint_name (str): Logical joint name
            
        Returns:
            str: Maya joint name or None if not found
        """
        return self.joints.get(joint_name)
    
    def get_all_joints(self):
        """
        Get all joints in the skeleton.
        
        Returns:
            dict: Dictionary of joint_name: maya_joint_name
        """
        return self.joints.copy()
    
    def mirror_joints(self, search_replace=("_L", "_R")):
        """
        Mirror joints from one side to another.
        
        Args:
            search_replace (tuple): Search and replace strings for mirroring
        """
        try:
            search, replace = search_replace
            joints_to_mirror = []
            
            # Find joints to mirror
            for joint_name, joint in self.joints.items():
                if search in joint_name:
                    joints_to_mirror.append(joint)
            
            if joints_to_mirror:
                # Mirror joints
                mirrored = cmds.mirrorJoint(
                    joints_to_mirror,
                    mirrorYZ=True,
                    mirrorBehavior=True,
                    searchReplace=[search, replace]
                )
                
                # Update joints dictionary
                for original, mirrored_joint in zip(joints_to_mirror, mirrored):
                    original_name = None
                    for name, joint in self.joints.items():
                        if joint == original:
                            original_name = name
                            break
                    
                    if original_name:
                        mirrored_name = original_name.replace(search, replace)
                        self.joints[mirrored_name] = mirrored_joint
                
                om.MGlobal.displayInfo(f"Successfully mirrored {len(joints_to_mirror)} joints")
                
        except Exception as e:
            om.MGlobal.displayError(f"Failed to mirror joints: {str(e)}")
            raise
