"""
控制器构建器模块

此模块提供创建和管理动画控制器的工具。
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om
from ..utils.naming import NamingConvention
from ..utils.maya_utils import get_object_world_position


class ControlBuilder:
    """
    为角色绑定构建动画控制器的主要类。
    """

    def __init__(self, character_name="character"):
        """
        初始化控制器构建器。

        Args:
            character_name (str): 角色的名称前缀
        """
        self.character_name = character_name
        self.naming = NamingConvention(character_name)
        self.controls = {}
        self.control_groups = {}

        # 定义控制器形状
        self.control_shapes = {
            "circle": self._create_circle_control,
            "square": self._create_square_control,
            "cube": self._create_cube_control,
            "sphere": self._create_sphere_control,
            "arrow": self._create_arrow_control,
            "cross": self._create_cross_control,
            "diamond": self._create_diamond_control,
            "pyramid": self._create_pyramid_control
        }

        # 控制器颜色（Maya颜色索引）
        self.control_colors = {
            "red": 13,
            "blue": 6,
            "yellow": 17,
            "green": 14,
            "purple": 9,
            "orange": 21,
            "pink": 20,
            "cyan": 18,
            "white": 16,
            "black": 1
        }
    
    def create_control(self, name, shape="circle", color="blue", size=1.0, position=(0, 0, 0), rotation=(0, 0, 0)):
        """
        使用指定参数创建控制器。

        Args:
            name (str): 控制器名称
            shape (str): 控制器形状类型
            color (str): 控制器颜色
            size (float): 控制器尺寸
            position (tuple): 世界位置
            rotation (tuple): 世界旋转

        Returns:
            tuple: (控制器变换, 控制器组)
        """
        try:
            # 创建控制器形状
            if shape not in self.control_shapes:
                raise ValueError(f"未知的控制器形状: {shape}")

            control = self.control_shapes[shape](name, size)

            # 创建控制器组（偏移组）
            group = cmds.group(control, name=self.naming.get_group_name(name))

            # 设置位置和旋转
            cmds.xform(group, worldSpace=True, translation=position, rotation=rotation)

            # 设置颜色
            self._set_control_color(control, color)

            # 存储引用
            self.controls[name] = control
            self.control_groups[name] = group

            om.MGlobal.displayInfo(f"已创建控制器: {control}")
            return control, group

        except Exception as e:
            om.MGlobal.displayError(f"创建控制器失败: {str(e)}")
            raise
    
    def create_control_for_joint(self, joint, shape="circle", color="blue", size=1.0, offset=(0, 0, 0)):
        """
        Create a control positioned at a joint.
        
        Args:
            joint (str): Joint name
            shape (str): Control shape type
            color (str): Control color
            size (float): Control size
            offset (tuple): Position offset from joint
            
        Returns:
            tuple: (control_transform, control_group)
        """
        try:
            if not cmds.objExists(joint):
                raise ValueError(f"Joint does not exist: {joint}")
            
            # Get joint position and rotation
            position = cmds.xform(joint, query=True, worldSpace=True, translation=True)
            rotation = cmds.xform(joint, query=True, worldSpace=True, rotation=True)
            
            # Apply offset
            position = [p + o for p, o in zip(position, offset)]
            
            # Extract joint name for control naming
            joint_base_name = joint.split(":")[-1].replace("_jnt", "").replace("_joint", "")
            
            # Create control
            return self.create_control(
                name=joint_base_name,
                shape=shape,
                color=color,
                size=size,
                position=position,
                rotation=rotation
            )
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create control for joint: {str(e)}")
            raise
    
    def _create_circle_control(self, name, size):
        """Create a circle control shape."""
        control = cmds.circle(name=self.naming.get_control_name(name), radius=size, normal=(0, 1, 0))[0]
        return control
    
    def _create_square_control(self, name, size):
        """Create a square control shape."""
        points = [
            (-size, 0, -size), (size, 0, -size), (size, 0, size), (-size, 0, size), (-size, 0, -size)
        ]
        control = cmds.curve(name=self.naming.get_control_name(name), point=points, degree=1)
        return control
    
    def _create_cube_control(self, name, size):
        """Create a cube control shape."""
        points = [
            (-size, -size, -size), (size, -size, -size), (size, size, -size), (-size, size, -size), (-size, -size, -size),
            (-size, -size, size), (size, -size, size), (size, size, size), (-size, size, size), (-size, -size, size),
            (-size, size, size), (-size, size, -size), (size, size, -size), (size, size, size), (size, -size, size), (size, -size, -size)
        ]
        control = cmds.curve(name=self.naming.get_control_name(name), point=points, degree=1)
        return control
    
    def _create_sphere_control(self, name, size):
        """Create a sphere control shape."""
        # Create three circles for sphere representation
        circle1 = cmds.circle(radius=size, normal=(1, 0, 0))[0]
        circle2 = cmds.circle(radius=size, normal=(0, 1, 0))[0]
        circle3 = cmds.circle(radius=size, normal=(0, 0, 1))[0]
        
        # Combine shapes
        shapes = cmds.listRelatives(circle1, shapes=True) + \
                cmds.listRelatives(circle2, shapes=True) + \
                cmds.listRelatives(circle3, shapes=True)
        
        # Create main control
        control = cmds.group(empty=True, name=self.naming.get_control_name(name))
        
        # Parent shapes to control
        for shape in shapes:
            cmds.parent(shape, control, relative=True, shape=True)
        
        # Delete temporary objects
        cmds.delete(circle1, circle2, circle3)
        
        return control
    
    def _create_arrow_control(self, name, size):
        """Create an arrow control shape."""
        points = [
            (0, 0, size*2), (-size*0.5, 0, size), (-size*0.25, 0, size),
            (-size*0.25, 0, -size), (size*0.25, 0, -size), (size*0.25, 0, size),
            (size*0.5, 0, size), (0, 0, size*2)
        ]
        control = cmds.curve(name=self.naming.get_control_name(name), point=points, degree=1)
        return control
    
    def _create_cross_control(self, name, size):
        """Create a cross control shape."""
        points = [
            (-size*0.2, 0, -size), (-size*0.2, 0, -size*0.2), (-size, 0, -size*0.2),
            (-size, 0, size*0.2), (-size*0.2, 0, size*0.2), (-size*0.2, 0, size),
            (size*0.2, 0, size), (size*0.2, 0, size*0.2), (size, 0, size*0.2),
            (size, 0, -size*0.2), (size*0.2, 0, -size*0.2), (size*0.2, 0, -size),
            (-size*0.2, 0, -size)
        ]
        control = cmds.curve(name=self.naming.get_control_name(name), point=points, degree=1)
        return control
    
    def _create_diamond_control(self, name, size):
        """Create a diamond control shape."""
        points = [
            (0, 0, size), (size, 0, 0), (0, 0, -size), (-size, 0, 0), (0, 0, size),
            (0, size, 0), (size, 0, 0), (0, -size, 0), (-size, 0, 0), (0, size, 0),
            (0, 0, -size), (0, -size, 0), (0, 0, size)
        ]
        control = cmds.curve(name=self.naming.get_control_name(name), point=points, degree=1)
        return control
    
    def _create_pyramid_control(self, name, size):
        """Create a pyramid control shape."""
        points = [
            (-size, 0, -size), (size, 0, -size), (0, size*2, 0), (-size, 0, -size),
            (-size, 0, size), (0, size*2, 0), (size, 0, size), (size, 0, -size),
            (0, size*2, 0), (size, 0, size), (-size, 0, size)
        ]
        control = cmds.curve(name=self.naming.get_control_name(name), point=points, degree=1)
        return control
    
    def _set_control_color(self, control, color):
        """
        Set the color of a control.
        
        Args:
            control (str): Control name
            color (str): Color name
        """
        try:
            if color not in self.control_colors:
                om.MGlobal.displayWarning(f"Unknown color: {color}, using blue")
                color = "blue"
            
            color_index = self.control_colors[color]
            
            # Get control shapes
            shapes = cmds.listRelatives(control, shapes=True)
            if shapes:
                for shape in shapes:
                    cmds.setAttr(f"{shape}.overrideEnabled", True)
                    cmds.setAttr(f"{shape}.overrideColor", color_index)
                    
        except Exception as e:
            om.MGlobal.displayWarning(f"Failed to set control color: {str(e)}")
    
    def constrain_control_to_joint(self, control_name, joint, constraint_type="parent"):
        """
        Constrain a control to a joint.
        
        Args:
            control_name (str): Control name
            joint (str): Joint name
            constraint_type (str): Type of constraint (parent, point, orient)
        """
        try:
            if control_name not in self.controls:
                raise ValueError(f"Control not found: {control_name}")
            
            control = self.controls[control_name]
            
            if not cmds.objExists(joint):
                raise ValueError(f"Joint does not exist: {joint}")
            
            if constraint_type == "parent":
                cmds.parentConstraint(control, joint, maintainOffset=True)
            elif constraint_type == "point":
                cmds.pointConstraint(control, joint, maintainOffset=True)
            elif constraint_type == "orient":
                cmds.orientConstraint(control, joint, maintainOffset=True)
            else:
                raise ValueError(f"Unknown constraint type: {constraint_type}")
                
            om.MGlobal.displayInfo(f"Constrained {joint} to {control} with {constraint_type} constraint")
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to constrain control: {str(e)}")
            raise
    
    def get_control(self, name):
        """
        Get a control by name.
        
        Args:
            name (str): Control name
            
        Returns:
            str: Control transform name or None
        """
        return self.controls.get(name)
    
    def get_control_group(self, name):
        """
        Get a control group by name.
        
        Args:
            name (str): Control name
            
        Returns:
            str: Control group name or None
        """
        return self.control_groups.get(name)
    
    def get_all_controls(self):
        """
        Get all controls.
        
        Returns:
            dict: Dictionary of control_name: control_transform
        """
        return self.controls.copy()
