"""
Constraint Tools Module

This module provides tools for creating and managing constraints in character rigs.
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om
from ..utils.naming import NamingConvention


class ConstraintTools:
    """
    Main class for constraint operations in character rigging.
    """
    
    def __init__(self, character_name="character"):
        """
        Initialize the constraint tools.
        
        Args:
            character_name (str): Name prefix for the character
        """
        self.character_name = character_name
        self.naming = NamingConvention(character_name)
        self.constraints = {}
    
    def create_parent_constraint(self, drivers, driven, maintain_offset=True, name=None):
        """
        Create a parent constraint.
        
        Args:
            drivers (list): List of driver objects
            driven (str): Driven object
            maintain_offset (bool): Maintain offset
            name (str): Custom constraint name
            
        Returns:
            str: Constraint name
        """
        try:
            if isinstance(drivers, str):
                drivers = [drivers]
            
            # Validate objects
            for driver in drivers:
                if not cmds.objExists(driver):
                    raise ValueError(f"Driver object does not exist: {driver}")
            
            if not cmds.objExists(driven):
                raise ValueError(f"Driven object does not exist: {driven}")
            
            # Create constraint
            constraint = cmds.parentConstraint(
                drivers, driven,
                maintainOffset=maintain_offset,
                name=name or self.naming.get_constraint_name(driven, "parent")
            )[0]
            
            # Store reference
            self.constraints[constraint] = {
                "type": "parent",
                "drivers": drivers,
                "driven": driven
            }
            
            om.MGlobal.displayInfo(f"Created parent constraint: {constraint}")
            return constraint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create parent constraint: {str(e)}")
            raise
    
    def create_point_constraint(self, drivers, driven, maintain_offset=True, name=None):
        """
        Create a point constraint.
        
        Args:
            drivers (list): List of driver objects
            driven (str): Driven object
            maintain_offset (bool): Maintain offset
            name (str): Custom constraint name
            
        Returns:
            str: Constraint name
        """
        try:
            if isinstance(drivers, str):
                drivers = [drivers]
            
            # Validate objects
            for driver in drivers:
                if not cmds.objExists(driver):
                    raise ValueError(f"Driver object does not exist: {driver}")
            
            if not cmds.objExists(driven):
                raise ValueError(f"Driven object does not exist: {driven}")
            
            # Create constraint
            constraint = cmds.pointConstraint(
                drivers, driven,
                maintainOffset=maintain_offset,
                name=name or self.naming.get_constraint_name(driven, "point")
            )[0]
            
            # Store reference
            self.constraints[constraint] = {
                "type": "point",
                "drivers": drivers,
                "driven": driven
            }
            
            om.MGlobal.displayInfo(f"Created point constraint: {constraint}")
            return constraint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create point constraint: {str(e)}")
            raise
    
    def create_orient_constraint(self, drivers, driven, maintain_offset=True, name=None):
        """
        Create an orient constraint.
        
        Args:
            drivers (list): List of driver objects
            driven (str): Driven object
            maintain_offset (bool): Maintain offset
            name (str): Custom constraint name
            
        Returns:
            str: Constraint name
        """
        try:
            if isinstance(drivers, str):
                drivers = [drivers]
            
            # Validate objects
            for driver in drivers:
                if not cmds.objExists(driver):
                    raise ValueError(f"Driver object does not exist: {driver}")
            
            if not cmds.objExists(driven):
                raise ValueError(f"Driven object does not exist: {driven}")
            
            # Create constraint
            constraint = cmds.orientConstraint(
                drivers, driven,
                maintainOffset=maintain_offset,
                name=name or self.naming.get_constraint_name(driven, "orient")
            )[0]
            
            # Store reference
            self.constraints[constraint] = {
                "type": "orient",
                "drivers": drivers,
                "driven": driven
            }
            
            om.MGlobal.displayInfo(f"Created orient constraint: {constraint}")
            return constraint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create orient constraint: {str(e)}")
            raise
    
    def create_scale_constraint(self, drivers, driven, maintain_offset=True, name=None):
        """
        Create a scale constraint.
        
        Args:
            drivers (list): List of driver objects
            driven (str): Driven object
            maintain_offset (bool): Maintain offset
            name (str): Custom constraint name
            
        Returns:
            str: Constraint name
        """
        try:
            if isinstance(drivers, str):
                drivers = [drivers]
            
            # Validate objects
            for driver in drivers:
                if not cmds.objExists(driver):
                    raise ValueError(f"Driver object does not exist: {driver}")
            
            if not cmds.objExists(driven):
                raise ValueError(f"Driven object does not exist: {driven}")
            
            # Create constraint
            constraint = cmds.scaleConstraint(
                drivers, driven,
                maintainOffset=maintain_offset,
                name=name or self.naming.get_constraint_name(driven, "scale")
            )[0]
            
            # Store reference
            self.constraints[constraint] = {
                "type": "scale",
                "drivers": drivers,
                "driven": driven
            }
            
            om.MGlobal.displayInfo(f"Created scale constraint: {constraint}")
            return constraint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create scale constraint: {str(e)}")
            raise
    
    def create_aim_constraint(self, target, driven, aim_vector=(1, 0, 0), up_vector=(0, 1, 0), 
                            world_up_type="vector", world_up_vector=(0, 1, 0), name=None):
        """
        Create an aim constraint.
        
        Args:
            target (str): Target object to aim at
            driven (str): Driven object
            aim_vector (tuple): Local aim vector
            up_vector (tuple): Local up vector
            world_up_type (str): World up type
            world_up_vector (tuple): World up vector
            name (str): Custom constraint name
            
        Returns:
            str: Constraint name
        """
        try:
            # Validate objects
            if not cmds.objExists(target):
                raise ValueError(f"Target object does not exist: {target}")
            
            if not cmds.objExists(driven):
                raise ValueError(f"Driven object does not exist: {driven}")
            
            # Create constraint
            constraint = cmds.aimConstraint(
                target, driven,
                aimVector=aim_vector,
                upVector=up_vector,
                worldUpType=world_up_type,
                worldUpVector=world_up_vector,
                name=name or self.naming.get_constraint_name(driven, "aim")
            )[0]
            
            # Store reference
            self.constraints[constraint] = {
                "type": "aim",
                "drivers": [target],
                "driven": driven
            }
            
            om.MGlobal.displayInfo(f"Created aim constraint: {constraint}")
            return constraint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create aim constraint: {str(e)}")
            raise
    
    def create_pole_vector_constraint(self, pole_vector, ik_handle, name=None):
        """
        Create a pole vector constraint for IK.
        
        Args:
            pole_vector (str): Pole vector control
            ik_handle (str): IK handle
            name (str): Custom constraint name
            
        Returns:
            str: Constraint name
        """
        try:
            # Validate objects
            if not cmds.objExists(pole_vector):
                raise ValueError(f"Pole vector object does not exist: {pole_vector}")
            
            if not cmds.objExists(ik_handle):
                raise ValueError(f"IK handle does not exist: {ik_handle}")
            
            # Create constraint
            constraint = cmds.poleVectorConstraint(
                pole_vector, ik_handle,
                name=name or self.naming.get_constraint_name(ik_handle, "poleVector")
            )[0]
            
            # Store reference
            self.constraints[constraint] = {
                "type": "poleVector",
                "drivers": [pole_vector],
                "driven": ik_handle
            }
            
            om.MGlobal.displayInfo(f"Created pole vector constraint: {constraint}")
            return constraint
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create pole vector constraint: {str(e)}")
            raise
    
    def set_constraint_weights(self, constraint, weights):
        """
        Set constraint weights.
        
        Args:
            constraint (str): Constraint name
            weights (list): List of weights for each driver
            
        Returns:
            bool: True if successful
        """
        try:
            if not cmds.objExists(constraint):
                raise ValueError(f"Constraint does not exist: {constraint}")
            
            constraint_type = cmds.objectType(constraint)
            
            # Get weight attributes based on constraint type
            if constraint_type == "parentConstraint":
                weight_attrs = cmds.parentConstraint(constraint, query=True, weightAliasList=True)
            elif constraint_type == "pointConstraint":
                weight_attrs = cmds.pointConstraint(constraint, query=True, weightAliasList=True)
            elif constraint_type == "orientConstraint":
                weight_attrs = cmds.orientConstraint(constraint, query=True, weightAliasList=True)
            elif constraint_type == "scaleConstraint":
                weight_attrs = cmds.scaleConstraint(constraint, query=True, weightAliasList=True)
            else:
                raise ValueError(f"Unsupported constraint type: {constraint_type}")
            
            # Set weights
            for i, weight in enumerate(weights):
                if i < len(weight_attrs):
                    cmds.setAttr(f"{constraint}.{weight_attrs[i]}", weight)
            
            om.MGlobal.displayInfo(f"Set weights for constraint: {constraint}")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to set constraint weights: {str(e)}")
            return False
    
    def remove_constraint(self, constraint):
        """
        Remove a constraint.
        
        Args:
            constraint (str): Constraint name
            
        Returns:
            bool: True if successful
        """
        try:
            if cmds.objExists(constraint):
                cmds.delete(constraint)
                
                # Remove from tracking
                if constraint in self.constraints:
                    del self.constraints[constraint]
                
                om.MGlobal.displayInfo(f"Removed constraint: {constraint}")
                return True
            else:
                om.MGlobal.displayWarning(f"Constraint does not exist: {constraint}")
                return False
                
        except Exception as e:
            om.MGlobal.displayError(f"Failed to remove constraint: {str(e)}")
            return False
    
    def get_constraint_info(self, constraint):
        """
        Get information about a constraint.
        
        Args:
            constraint (str): Constraint name
            
        Returns:
            dict: Constraint information
        """
        if constraint in self.constraints:
            return self.constraints[constraint].copy()
        return {}
    
    def get_all_constraints(self):
        """
        Get all constraints managed by this instance.
        
        Returns:
            dict: Dictionary of constraint information
        """
        return self.constraints.copy()
