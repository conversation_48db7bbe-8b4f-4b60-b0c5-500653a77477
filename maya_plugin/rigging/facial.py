"""
Facial Rigging Module

This module provides tools for facial rigging and expression setup.
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om
from ..utils.naming import NamingConvention
from .controls import ControlBuilder


class FacialRigger:
    """
    Main class for facial rigging operations.
    """
    
    def __init__(self, character_name="character"):
        """
        Initialize the facial rigger.
        
        Args:
            character_name (str): Name prefix for the character
        """
        self.character_name = character_name
        self.naming = NamingConvention(character_name)
        self.control_builder = ControlBuilder(character_name)
        self.blendshapes = {}
        self.facial_controls = {}
        
        # Define facial regions and their controls
        self.facial_regions = {
            "eyebrows": {
                "controls": ["eyebrow_inner_L", "eyebrow_mid_L", "eyebrow_outer_L",
                           "eyebrow_inner_R", "eyebrow_mid_R", "eyebrow_outer_R"],
                "color": "yellow"
            },
            "eyelids": {
                "controls": ["eyelid_upper_L", "eyelid_lower_L", 
                           "eyelid_upper_R", "eyelid_lower_R"],
                "color": "green"
            },
            "eyes": {
                "controls": ["eye_L", "eye_R"],
                "color": "cyan"
            },
            "cheeks": {
                "controls": ["cheek_L", "cheek_R"],
                "color": "orange"
            },
            "mouth": {
                "controls": ["mouth_corner_L", "mouth_corner_R", "mouth_upper", 
                           "mouth_lower", "lip_upper_L", "lip_upper_R", 
                           "lip_lower_L", "lip_lower_R"],
                "color": "red"
            },
            "jaw": {
                "controls": ["jaw"],
                "color": "purple"
            }
        }
    
    def create_facial_controls(self, head_joint=None, scale=1.0):
        """
        Create facial animation controls.
        
        Args:
            head_joint (str): Head joint to position controls relative to
            scale (float): Scale factor for controls
            
        Returns:
            dict: Dictionary of created controls by region
        """
        try:
            created_controls = {}
            
            # Get head position for reference
            head_pos = (0, 18, 0)  # Default position
            if head_joint and cmds.objExists(head_joint):
                head_pos = cmds.xform(head_joint, query=True, worldSpace=True, translation=True)
            
            # Create controls for each region
            for region, region_data in self.facial_regions.items():
                created_controls[region] = []
                
                for control_name in region_data["controls"]:
                    # Calculate control position based on name
                    position = self._calculate_control_position(control_name, head_pos, scale)
                    
                    # Create control
                    control, group = self.control_builder.create_control(
                        name=control_name,
                        shape="circle",
                        color=region_data["color"],
                        size=scale * 0.5,
                        position=position
                    )
                    
                    created_controls[region].append(control)
                    self.facial_controls[control_name] = {
                        "control": control,
                        "group": group,
                        "region": region
                    }
            
            om.MGlobal.displayInfo(f"Created facial controls for {len(self.facial_regions)} regions")
            return created_controls
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create facial controls: {str(e)}")
            raise
    
    def _calculate_control_position(self, control_name, head_pos, scale):
        """
        Calculate control position based on control name and head position.
        
        Args:
            control_name (str): Name of the control
            head_pos (tuple): Head position
            scale (float): Scale factor
            
        Returns:
            tuple: Control position
        """
        x, y, z = head_pos
        offset_scale = scale * 2.0
        
        # Define position offsets based on control name
        position_offsets = {
            # Eyebrows
            "eyebrow_inner_L": (offset_scale * 0.5, offset_scale * 1.5, offset_scale * 1.0),
            "eyebrow_mid_L": (offset_scale * 1.0, offset_scale * 1.5, offset_scale * 1.0),
            "eyebrow_outer_L": (offset_scale * 1.5, offset_scale * 1.2, offset_scale * 0.8),
            "eyebrow_inner_R": (-offset_scale * 0.5, offset_scale * 1.5, offset_scale * 1.0),
            "eyebrow_mid_R": (-offset_scale * 1.0, offset_scale * 1.5, offset_scale * 1.0),
            "eyebrow_outer_R": (-offset_scale * 1.5, offset_scale * 1.2, offset_scale * 0.8),
            
            # Eyelids
            "eyelid_upper_L": (offset_scale * 0.8, offset_scale * 1.0, offset_scale * 1.0),
            "eyelid_lower_L": (offset_scale * 0.8, offset_scale * 0.8, offset_scale * 1.0),
            "eyelid_upper_R": (-offset_scale * 0.8, offset_scale * 1.0, offset_scale * 1.0),
            "eyelid_lower_R": (-offset_scale * 0.8, offset_scale * 0.8, offset_scale * 1.0),
            
            # Eyes
            "eye_L": (offset_scale * 0.8, offset_scale * 0.9, offset_scale * 1.2),
            "eye_R": (-offset_scale * 0.8, offset_scale * 0.9, offset_scale * 1.2),
            
            # Cheeks
            "cheek_L": (offset_scale * 1.2, offset_scale * 0.5, offset_scale * 0.8),
            "cheek_R": (-offset_scale * 1.2, offset_scale * 0.5, offset_scale * 0.8),
            
            # Mouth
            "mouth_corner_L": (offset_scale * 0.6, offset_scale * 0.2, offset_scale * 1.0),
            "mouth_corner_R": (-offset_scale * 0.6, offset_scale * 0.2, offset_scale * 1.0),
            "mouth_upper": (0, offset_scale * 0.4, offset_scale * 1.0),
            "mouth_lower": (0, offset_scale * 0.0, offset_scale * 1.0),
            "lip_upper_L": (offset_scale * 0.3, offset_scale * 0.4, offset_scale * 1.0),
            "lip_upper_R": (-offset_scale * 0.3, offset_scale * 0.4, offset_scale * 1.0),
            "lip_lower_L": (offset_scale * 0.3, offset_scale * 0.0, offset_scale * 1.0),
            "lip_lower_R": (-offset_scale * 0.3, offset_scale * 0.0, offset_scale * 1.0),
            
            # Jaw
            "jaw": (0, offset_scale * -0.5, offset_scale * 0.8)
        }
        
        offset = position_offsets.get(control_name, (0, 0, 0))
        return (x + offset[0], y + offset[1], z + offset[2])
    
    def create_blendshape_setup(self, base_mesh, target_meshes):
        """
        Create a blendshape setup for facial expressions.
        
        Args:
            base_mesh (str): Base facial mesh
            target_meshes (list): List of target expression meshes
            
        Returns:
            str: Created blendshape node name
        """
        try:
            if not cmds.objExists(base_mesh):
                raise ValueError(f"Base mesh does not exist: {base_mesh}")
            
            # Validate target meshes
            valid_targets = []
            for target in target_meshes:
                if cmds.objExists(target):
                    valid_targets.append(target)
                else:
                    om.MGlobal.displayWarning(f"Target mesh does not exist: {target}")
            
            if not valid_targets:
                raise ValueError("No valid target meshes provided")
            
            # Create blendshape
            blendshape_name = self.naming.get_group_name("facial", group_type="bs")
            blendshape = cmds.blendShape(
                valid_targets, base_mesh,
                name=blendshape_name,
                frontOfChain=True
            )[0]
            
            # Store reference
            self.blendshapes[base_mesh] = {
                "node": blendshape,
                "targets": valid_targets
            }
            
            om.MGlobal.displayInfo(f"Created blendshape: {blendshape} with {len(valid_targets)} targets")
            return blendshape
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create blendshape setup: {str(e)}")
            raise
    
    def connect_controls_to_blendshape(self, blendshape, control_mapping):
        """
        Connect facial controls to blendshape targets.
        
        Args:
            blendshape (str): Blendshape node name
            control_mapping (dict): Dictionary mapping control names to blendshape targets
            
        Returns:
            bool: True if successful
        """
        try:
            if not cmds.objExists(blendshape):
                raise ValueError(f"Blendshape does not exist: {blendshape}")
            
            connections_made = 0
            
            for control_name, target_name in control_mapping.items():
                if control_name in self.facial_controls:
                    control = self.facial_controls[control_name]["control"]
                    
                    # Add custom attribute to control if it doesn't exist
                    attr_name = f"{target_name}_weight"
                    if not cmds.attributeQuery(attr_name, node=control, exists=True):
                        cmds.addAttr(control, longName=attr_name, attributeType="float", 
                                   min=0.0, max=1.0, defaultValue=0.0, keyable=True)
                    
                    # Connect to blendshape
                    cmds.connectAttr(f"{control}.{attr_name}", f"{blendshape}.{target_name}")
                    connections_made += 1
                else:
                    om.MGlobal.displayWarning(f"Control not found: {control_name}")
            
            om.MGlobal.displayInfo(f"Connected {connections_made} controls to blendshape")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to connect controls to blendshape: {str(e)}")
            return False
    
    def create_eye_rig(self, eye_joints, look_at_distance=10.0):
        """
        Create an eye rigging setup with look-at controls.
        
        Args:
            eye_joints (list): List of eye joint names
            look_at_distance (float): Distance for look-at target
            
        Returns:
            dict: Dictionary of created eye rig components
        """
        try:
            eye_rig = {}
            
            for eye_joint in eye_joints:
                if not cmds.objExists(eye_joint):
                    om.MGlobal.displayWarning(f"Eye joint does not exist: {eye_joint}")
                    continue
                
                # Determine side
                side = "L" if "_L" in eye_joint else "R" if "_R" in eye_joint else "C"
                
                # Get eye position
                eye_pos = cmds.xform(eye_joint, query=True, worldSpace=True, translation=True)
                
                # Create look-at target
                target_pos = (eye_pos[0], eye_pos[1], eye_pos[2] + look_at_distance)
                target_control, target_group = self.control_builder.create_control(
                    name=f"eye_target_{side}",
                    shape="diamond",
                    color="cyan",
                    size=1.0,
                    position=target_pos
                )
                
                # Create aim constraint
                from .constraints import ConstraintTools
                constraint_tools = ConstraintTools(self.character_name)
                aim_constraint = constraint_tools.create_aim_constraint(
                    target_control, eye_joint,
                    aim_vector=(0, 0, 1),
                    up_vector=(0, 1, 0)
                )
                
                eye_rig[eye_joint] = {
                    "target_control": target_control,
                    "target_group": target_group,
                    "aim_constraint": aim_constraint
                }
            
            om.MGlobal.displayInfo(f"Created eye rig for {len(eye_rig)} eyes")
            return eye_rig
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to create eye rig: {str(e)}")
            raise
    
    def get_facial_control(self, control_name):
        """
        Get a facial control by name.
        
        Args:
            control_name (str): Control name
            
        Returns:
            dict: Control information or None
        """
        return self.facial_controls.get(control_name)
    
    def get_all_facial_controls(self):
        """
        Get all facial controls.
        
        Returns:
            dict: Dictionary of all facial controls
        """
        return self.facial_controls.copy()
    
    def get_blendshape_info(self, mesh):
        """
        Get blendshape information for a mesh.
        
        Args:
            mesh (str): Mesh name
            
        Returns:
            dict: Blendshape information or None
        """
        return self.blendshapes.get(mesh)
