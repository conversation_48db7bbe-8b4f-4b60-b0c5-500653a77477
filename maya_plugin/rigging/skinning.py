"""
Skinning Tools Module

This module provides tools for character skinning and weight management.
"""

import maya.cmds as cmds
import maya.api.OpenMaya as om
from ..utils.naming import NamingConvention


class SkinningTools:
    """
    Main class for character skinning operations.
    """
    
    def __init__(self, character_name="character"):
        """
        Initialize the skinning tools.
        
        Args:
            character_name (str): Name prefix for the character
        """
        self.character_name = character_name
        self.naming = NamingConvention(character_name)
        self.skin_clusters = {}
    
    def auto_skin_mesh(self, mesh, joints, method="closest", max_influences=4):
        """
        Automatically skin a mesh to joints.
        
        Args:
            mesh (str): Mesh to skin
            joints (list): List of joints to bind to
            method (str): Skinning method ("closest", "heat", "geodesic")
            max_influences (int): Maximum influences per vertex
            
        Returns:
            str: Created skin cluster name
        """
        try:
            if not cmds.objExists(mesh):
                raise ValueError(f"Mesh does not exist: {mesh}")
            
            # Validate joints
            valid_joints = []
            for joint in joints:
                if cmds.objExists(joint) and cmds.objectType(joint) == "joint":
                    valid_joints.append(joint)
                else:
                    om.MGlobal.displayWarning(f"Invalid joint: {joint}")
            
            if not valid_joints:
                raise ValueError("No valid joints provided for skinning")
            
            # Create skin cluster
            skin_cluster = cmds.skinCluster(
                valid_joints, mesh,
                name=self.naming.get_group_name("skinCluster", group_type="sc"),
                toSelectedBones=True,
                bindMethod=0 if method == "closest" else 1,
                maximumInfluences=max_influences,
                normalizeWeights=1,
                weightDistribution=0,
                removeUnusedInfluence=False
            )[0]
            
            # Store reference
            self.skin_clusters[mesh] = skin_cluster
            
            om.MGlobal.displayInfo(f"Successfully skinned {mesh} to {len(valid_joints)} joints")
            return skin_cluster
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to skin mesh: {str(e)}")
            raise
    
    def copy_skin_weights(self, source_mesh, target_mesh, surface_association="closestPoint"):
        """
        Copy skin weights from source mesh to target mesh.
        
        Args:
            source_mesh (str): Source mesh with existing skinning
            target_mesh (str): Target mesh to copy weights to
            surface_association (str): Method for weight transfer
            
        Returns:
            bool: True if successful
        """
        try:
            # Validate meshes
            if not cmds.objExists(source_mesh):
                raise ValueError(f"Source mesh does not exist: {source_mesh}")
            
            if not cmds.objExists(target_mesh):
                raise ValueError(f"Target mesh does not exist: {target_mesh}")
            
            # Get source skin cluster
            source_skin = self._get_skin_cluster(source_mesh)
            if not source_skin:
                raise ValueError(f"Source mesh has no skin cluster: {source_mesh}")
            
            # Get joints from source skin cluster
            joints = cmds.skinCluster(source_skin, query=True, influence=True)
            
            # Create skin cluster on target mesh
            target_skin = self.auto_skin_mesh(target_mesh, joints)
            
            # Copy weights
            cmds.copySkinWeights(
                sourceSkin=source_skin,
                destinationSkin=target_skin,
                noMirror=True,
                surfaceAssociation=surface_association,
                influenceAssociation="name"
            )
            
            om.MGlobal.displayInfo(f"Successfully copied weights from {source_mesh} to {target_mesh}")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to copy skin weights: {str(e)}")
            return False
    
    def smooth_skin_weights(self, mesh, iterations=3, strength=0.5):
        """
        Smooth skin weights on a mesh.
        
        Args:
            mesh (str): Mesh to smooth weights on
            iterations (int): Number of smoothing iterations
            strength (float): Smoothing strength (0.0 to 1.0)
            
        Returns:
            bool: True if successful
        """
        try:
            skin_cluster = self._get_skin_cluster(mesh)
            if not skin_cluster:
                raise ValueError(f"Mesh has no skin cluster: {mesh}")
            
            # Select all vertices
            vertex_count = cmds.polyEvaluate(mesh, vertex=True)
            vertices = [f"{mesh}.vtx[{i}]" for i in range(vertex_count)]
            cmds.select(vertices)
            
            # Smooth weights
            for i in range(iterations):
                cmds.skinPercent(skin_cluster, vertices, pruneWeights=0.01)
                
                # Apply smoothing
                mel_command = f'smoothSkinWeights "{skin_cluster}" {strength}'
                try:
                    cmds.mel.eval(mel_command)
                except:
                    # Fallback method
                    cmds.skinPercent(skin_cluster, vertices, normalize=True)
            
            om.MGlobal.displayInfo(f"Smoothed weights on {mesh} ({iterations} iterations)")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to smooth skin weights: {str(e)}")
            return False
    
    def normalize_skin_weights(self, mesh):
        """
        Normalize skin weights on a mesh.
        
        Args:
            mesh (str): Mesh to normalize weights on
            
        Returns:
            bool: True if successful
        """
        try:
            skin_cluster = self._get_skin_cluster(mesh)
            if not skin_cluster:
                raise ValueError(f"Mesh has no skin cluster: {mesh}")
            
            # Get all vertices
            vertex_count = cmds.polyEvaluate(mesh, vertex=True)
            vertices = [f"{mesh}.vtx[{i}]" for i in range(vertex_count)]
            
            # Normalize weights
            cmds.skinPercent(skin_cluster, vertices, normalize=True)
            
            om.MGlobal.displayInfo(f"Normalized weights on {mesh}")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to normalize skin weights: {str(e)}")
            return False
    
    def prune_skin_weights(self, mesh, threshold=0.01):
        """
        Prune small skin weights below threshold.
        
        Args:
            mesh (str): Mesh to prune weights on
            threshold (float): Weight threshold for pruning
            
        Returns:
            bool: True if successful
        """
        try:
            skin_cluster = self._get_skin_cluster(mesh)
            if not skin_cluster:
                raise ValueError(f"Mesh has no skin cluster: {mesh}")
            
            # Get all vertices
            vertex_count = cmds.polyEvaluate(mesh, vertex=True)
            vertices = [f"{mesh}.vtx[{i}]" for i in range(vertex_count)]
            
            # Prune weights
            cmds.skinPercent(skin_cluster, vertices, pruneWeights=threshold)
            
            om.MGlobal.displayInfo(f"Pruned weights below {threshold} on {mesh}")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to prune skin weights: {str(e)}")
            return False
    
    def lock_joint_weights(self, mesh, joints, lock=True):
        """
        Lock or unlock joint weights.
        
        Args:
            mesh (str): Mesh with skin cluster
            joints (list): List of joints to lock/unlock
            lock (bool): True to lock, False to unlock
            
        Returns:
            bool: True if successful
        """
        try:
            skin_cluster = self._get_skin_cluster(mesh)
            if not skin_cluster:
                raise ValueError(f"Mesh has no skin cluster: {mesh}")
            
            for joint in joints:
                if cmds.objExists(joint):
                    cmds.setAttr(f"{skin_cluster}.lockWeights[{joint}]", lock)
            
            action = "Locked" if lock else "Unlocked"
            om.MGlobal.displayInfo(f"{action} weights for {len(joints)} joints on {mesh}")
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to lock/unlock joint weights: {str(e)}")
            return False
    
    def get_skin_weights(self, mesh, vertex_index):
        """
        Get skin weights for a specific vertex.
        
        Args:
            mesh (str): Mesh with skin cluster
            vertex_index (int): Vertex index
            
        Returns:
            dict: Dictionary of joint: weight pairs
        """
        try:
            skin_cluster = self._get_skin_cluster(mesh)
            if not skin_cluster:
                raise ValueError(f"Mesh has no skin cluster: {mesh}")
            
            vertex = f"{mesh}.vtx[{vertex_index}]"
            weights = cmds.skinPercent(skin_cluster, vertex, query=True, value=True)
            joints = cmds.skinCluster(skin_cluster, query=True, influence=True)
            
            return dict(zip(joints, weights))
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to get skin weights: {str(e)}")
            return {}
    
    def set_skin_weights(self, mesh, vertex_index, weight_dict):
        """
        Set skin weights for a specific vertex.
        
        Args:
            mesh (str): Mesh with skin cluster
            vertex_index (int): Vertex index
            weight_dict (dict): Dictionary of joint: weight pairs
            
        Returns:
            bool: True if successful
        """
        try:
            skin_cluster = self._get_skin_cluster(mesh)
            if not skin_cluster:
                raise ValueError(f"Mesh has no skin cluster: {mesh}")
            
            vertex = f"{mesh}.vtx[{vertex_index}]"
            
            # Set weights
            for joint, weight in weight_dict.items():
                if cmds.objExists(joint):
                    cmds.skinPercent(skin_cluster, vertex, transformValue=[(joint, weight)])
            
            # Normalize
            cmds.skinPercent(skin_cluster, vertex, normalize=True)
            
            return True
            
        except Exception as e:
            om.MGlobal.displayError(f"Failed to set skin weights: {str(e)}")
            return False
    
    def _get_skin_cluster(self, mesh):
        """
        Get the skin cluster for a mesh.
        
        Args:
            mesh (str): Mesh name
            
        Returns:
            str: Skin cluster name or None
        """
        try:
            # Check if we have it cached
            if mesh in self.skin_clusters:
                skin_cluster = self.skin_clusters[mesh]
                if cmds.objExists(skin_cluster):
                    return skin_cluster
            
            # Find skin cluster
            history = cmds.listHistory(mesh, pruneDagObjects=True)
            if history:
                skin_clusters = cmds.ls(history, type="skinCluster")
                if skin_clusters:
                    skin_cluster = skin_clusters[0]
                    self.skin_clusters[mesh] = skin_cluster
                    return skin_cluster
            
            return None
            
        except Exception:
            return None
    
    def get_all_skin_clusters(self):
        """
        Get all skin clusters managed by this instance.
        
        Returns:
            dict: Dictionary of mesh: skin_cluster pairs
        """
        return self.skin_clusters.copy()
